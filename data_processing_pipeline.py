"""
Intelligent Data Processing Pipeline
This module provides advanced NLP processing, entity extraction, and automated
data quality scoring for IPO company information.
"""

import asyncio
import re
import json
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, field
from datetime import datetime
import time

import nltk
import spacy
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.corpus import stopwords
from nltk.sentiment import SentimentIntensityAnalyzer
from textstat import flesch_reading_ease, flesch_kincaid_grade
from loguru import logger

from models import Company, CompanyDataSource
from database import get_db_session
from enhanced_data_collector import ExtractedData
from website_scraper import CompanyWebsiteScraper
from industry_classifier import EnhancedIndustryClassifier
from data_validator import DataValidator


# Download required NLTK data
try:
    nltk.data.find('vader_lexicon')
except LookupError:
    nltk.download('vader_lexicon')

# Load spaCy model
try:
    nlp = spacy.load("en_core_web_sm")
except OSError:
    logger.warning("spaCy model 'en_core_web_sm' not found. Install with: python -m spacy download en_core_web_sm")
    nlp = None


@dataclass
class ProcessedEntity:
    """Represents an extracted entity"""
    text: str
    label: str
    confidence: float
    start_pos: int
    end_pos: int


@dataclass
class TextAnalysis:
    """Results of text analysis"""
    sentiment_score: float
    readability_score: float
    key_phrases: List[str]
    entities: List[ProcessedEntity]
    summary: str
    language: str = "en"
    word_count: int = 0
    sentence_count: int = 0


@dataclass
class ProcessingResult:
    """Complete processing result for company data"""
    company_id: int
    extracted_data: ExtractedData
    text_analysis: TextAnalysis
    industry_classifications: List[Any]
    validation_report: Any
    quality_score: float
    processing_time: float
    recommendations: List[str]


class IntelligentDataProcessor:
    """
    Advanced data processing pipeline that combines multiple AI techniques
    to extract, analyze, and validate company information
    """
    
    def __init__(self):
        self.sentiment_analyzer = SentimentIntensityAnalyzer()
        self.website_scraper = None
        self.industry_classifier = EnhancedIndustryClassifier()
        self.data_validator = DataValidator()
        
        # Entity extraction patterns
        self.entity_patterns = self._load_entity_patterns()
        
        # Quality scoring weights
        self.quality_weights = {
            'completeness': 0.3,
            'accuracy': 0.25,
            'consistency': 0.2,
            'freshness': 0.15,
            'reliability': 0.1
        }
    
    async def process_company_data(self, company: Company, force_refresh: bool = False) -> ProcessingResult:
        """
        Process comprehensive company data using the full pipeline
        
        Args:
            company: Company object to process
            force_refresh: Whether to force refresh of all data
            
        Returns:
            ProcessingResult with all analysis results
        """
        logger.info(f"Starting intelligent processing for {company.name}")
        start_time = time.time()
        
        try:
            # Step 1: Extract fresh data from website if needed
            extracted_data = await self._extract_website_data(company, force_refresh)
            
            # Step 2: Perform NLP analysis on collected text
            text_analysis = self._analyze_text_content(extracted_data)
            
            # Step 3: Classify industry using multiple methods
            industry_classifications = self._classify_industry(company, extracted_data, text_analysis)
            
            # Step 4: Validate data consistency across sources
            validation_report = self._validate_data_quality(company)
            
            # Step 5: Calculate overall quality score
            quality_score = self._calculate_quality_score(
                extracted_data, text_analysis, industry_classifications, validation_report
            )
            
            # Step 6: Generate actionable recommendations
            recommendations = self._generate_recommendations(
                extracted_data, text_analysis, industry_classifications, validation_report
            )
            
            processing_time = time.time() - start_time
            
            # Step 7: Update company record with processed data
            await self._update_company_record(company, extracted_data, quality_score)
            
            logger.info(f"Processing completed for {company.name} in {processing_time:.2f}s")
            
            return ProcessingResult(
                company_id=company.id,
                extracted_data=extracted_data,
                text_analysis=text_analysis,
                industry_classifications=industry_classifications,
                validation_report=validation_report,
                quality_score=quality_score,
                processing_time=processing_time,
                recommendations=recommendations
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Processing failed for {company.name}: {e}")
            
            # Return minimal result with error information
            return ProcessingResult(
                company_id=company.id,
                extracted_data=ExtractedData(company_name=company.name),
                text_analysis=TextAnalysis(
                    sentiment_score=0.0,
                    readability_score=0.0,
                    key_phrases=[],
                    entities=[],
                    summary=f"Processing failed: {str(e)}"
                ),
                industry_classifications=[],
                validation_report=None,
                quality_score=0.0,
                processing_time=processing_time,
                recommendations=[f"Manual review required due to processing error: {str(e)}"]
            )
    
    async def _extract_website_data(self, company: Company, force_refresh: bool) -> ExtractedData:
        """Extract data from company website"""
        
        # Check if we have recent website data
        if not force_refresh:
            with get_db_session() as session:
                recent_data = session.query(CompanyDataSource).filter(
                    CompanyDataSource.company_id == company.id,
                    CompanyDataSource.source_type == 'website',
                    CompanyDataSource.status == 'active'
                ).order_by(CompanyDataSource.extraction_date.desc()).first()
                
                if recent_data and recent_data.extraction_date:
                    # Use cached data if less than 7 days old
                    age = datetime.utcnow() - recent_data.extraction_date
                    if age.days < 7:
                        logger.info(f"Using cached website data for {company.name}")
                        return self._convert_source_to_extracted_data(recent_data)
        
        # Extract fresh data from website
        if company.website:
            try:
                async with CompanyWebsiteScraper() as scraper:
                    extracted_data = await scraper.extract_company_data(company.website, company.name)
                    if extracted_data:
                        logger.info(f"Extracted fresh website data for {company.name}")
                        return extracted_data
            except Exception as e:
                logger.warning(f"Website scraping failed for {company.name}: {e}")
        
        # Fallback to existing company data
        return ExtractedData(
            company_name=company.name,
            industry=company.industry,
            sector=company.sector,
            description=company.description,
            website=company.website,
            headquarters=company.headquarters,
            ceo_name=company.ceo_name,
            founded_year=company.founded_year,
            employee_count=company.employee_count
        )
    
    def _analyze_text_content(self, extracted_data: ExtractedData) -> TextAnalysis:
        """Perform comprehensive NLP analysis on text content"""
        
        # Combine all text content
        text_content = []
        if extracted_data.description:
            text_content.append(extracted_data.description)
        if extracted_data.company_name:
            text_content.append(extracted_data.company_name)
        
        full_text = " ".join(text_content)
        
        if not full_text.strip():
            return TextAnalysis(
                sentiment_score=0.0,
                readability_score=0.0,
                key_phrases=[],
                entities=[],
                summary="No text content available for analysis"
            )
        
        # Sentiment analysis
        sentiment_score = self._analyze_sentiment(full_text)
        
        # Readability analysis
        readability_score = self._analyze_readability(full_text)
        
        # Key phrase extraction
        key_phrases = self._extract_key_phrases(full_text)
        
        # Named entity recognition
        entities = self._extract_entities(full_text)
        
        # Text summarization
        summary = self._generate_summary(full_text)
        
        # Basic text statistics
        sentences = sent_tokenize(full_text)
        words = word_tokenize(full_text)
        
        return TextAnalysis(
            sentiment_score=sentiment_score,
            readability_score=readability_score,
            key_phrases=key_phrases,
            entities=entities,
            summary=summary,
            word_count=len(words),
            sentence_count=len(sentences)
        )
    
    def _analyze_sentiment(self, text: str) -> float:
        """Analyze sentiment of text content"""
        try:
            scores = self.sentiment_analyzer.polarity_scores(text)
            # Convert compound score (-1 to 1) to 0-1 scale
            return (scores['compound'] + 1) / 2
        except Exception as e:
            logger.debug(f"Sentiment analysis failed: {e}")
            return 0.5  # Neutral sentiment as fallback
    
    def _analyze_readability(self, text: str) -> float:
        """Analyze readability of text content"""
        try:
            # Use Flesch Reading Ease score (0-100, higher is more readable)
            flesch_score = flesch_reading_ease(text)
            # Normalize to 0-1 scale
            return min(1.0, max(0.0, flesch_score / 100))
        except Exception as e:
            logger.debug(f"Readability analysis failed: {e}")
            return 0.5  # Average readability as fallback
    
    def _extract_key_phrases(self, text: str) -> List[str]:
        """Extract key phrases from text"""
        try:
            if not nlp:
                return []
            
            doc = nlp(text)
            
            # Extract noun phrases and named entities
            key_phrases = []
            
            # Add noun phrases
            for chunk in doc.noun_chunks:
                if len(chunk.text.split()) >= 2 and len(chunk.text) > 5:
                    key_phrases.append(chunk.text.strip())
            
            # Add named entities
            for ent in doc.ents:
                if ent.label_ in ['ORG', 'PRODUCT', 'TECHNOLOGY', 'INDUSTRY']:
                    key_phrases.append(ent.text.strip())
            
            # Remove duplicates and return top phrases
            unique_phrases = list(set(key_phrases))
            return unique_phrases[:10]  # Return top 10 phrases
            
        except Exception as e:
            logger.debug(f"Key phrase extraction failed: {e}")
            return []
    
    def _extract_entities(self, text: str) -> List[ProcessedEntity]:
        """Extract named entities from text"""
        try:
            if not nlp:
                return []
            
            doc = nlp(text)
            entities = []
            
            for ent in doc.ents:
                # Filter for relevant entity types
                if ent.label_ in ['PERSON', 'ORG', 'GPE', 'MONEY', 'DATE', 'PRODUCT']:
                    entities.append(ProcessedEntity(
                        text=ent.text,
                        label=ent.label_,
                        confidence=0.8,  # spaCy doesn't provide confidence scores
                        start_pos=ent.start_char,
                        end_pos=ent.end_char
                    ))
            
            return entities
            
        except Exception as e:
            logger.debug(f"Entity extraction failed: {e}")
            return []
    
    def _generate_summary(self, text: str) -> str:
        """Generate a summary of the text content"""
        try:
            sentences = sent_tokenize(text)
            
            if len(sentences) <= 2:
                return text
            
            # Simple extractive summarization - take first and most informative sentences
            first_sentence = sentences[0]
            
            # Find sentence with most business-related keywords
            business_keywords = ['company', 'business', 'provide', 'develop', 'specialize', 'focus', 'leader']
            
            best_sentence = ""
            best_score = 0
            
            for sentence in sentences[1:]:
                score = sum(1 for keyword in business_keywords if keyword.lower() in sentence.lower())
                if score > best_score:
                    best_score = score
                    best_sentence = sentence
            
            if best_sentence:
                return f"{first_sentence} {best_sentence}"
            else:
                return first_sentence
                
        except Exception as e:
            logger.debug(f"Summary generation failed: {e}")
            return text[:200] + "..." if len(text) > 200 else text

    def _classify_industry(self, company: Company, extracted_data: ExtractedData, text_analysis: TextAnalysis):
        """Classify company industry using enhanced classifier"""
        try:
            return self.industry_classifier.classify_company(
                company,
                description=extracted_data.description,
                website_keywords=extracted_data.keywords
            )
        except Exception as e:
            logger.error(f"Industry classification failed: {e}")
            return []

    def _validate_data_quality(self, company: Company):
        """Validate data quality using data validator"""
        try:
            return self.data_validator.validate_company_data(company)
        except Exception as e:
            logger.error(f"Data validation failed: {e}")
            return None

    def _calculate_quality_score(
        self,
        extracted_data: ExtractedData,
        text_analysis: TextAnalysis,
        industry_classifications: List[Any],
        validation_report: Any
    ) -> float:
        """Calculate overall data quality score"""
        scores = []

        # Completeness score (0-1)
        completeness = self._calculate_completeness_score(extracted_data)
        scores.append(completeness * self.quality_weights['completeness'])

        # Text quality score (0-1)
        text_quality = self._calculate_text_quality_score(text_analysis)
        scores.append(text_quality * self.quality_weights['accuracy'])

        # Classification confidence (0-1)
        classification_confidence = self._calculate_classification_confidence(industry_classifications)
        scores.append(classification_confidence * self.quality_weights['consistency'])

        # Validation score (0-1)
        validation_score = validation_report.overall_score if validation_report else 0.5
        scores.append(validation_score * self.quality_weights['reliability'])

        # Freshness score (always 1.0 for newly processed data)
        scores.append(1.0 * self.quality_weights['freshness'])

        return sum(scores)

    def _calculate_completeness_score(self, extracted_data: ExtractedData) -> float:
        """Calculate data completeness score"""
        fields = [
            extracted_data.company_name,
            extracted_data.industry,
            extracted_data.sector,
            extracted_data.description,
            extracted_data.website,
            extracted_data.headquarters,
            extracted_data.ceo_name,
            extracted_data.founded_year,
            extracted_data.employee_count
        ]

        filled_fields = sum(1 for field in fields if field is not None)
        return filled_fields / len(fields)

    def _calculate_text_quality_score(self, text_analysis: TextAnalysis) -> float:
        """Calculate text quality score based on analysis"""
        scores = []

        # Readability score
        scores.append(text_analysis.readability_score)

        # Content richness (based on word count and entities)
        if text_analysis.word_count > 50:
            content_score = min(1.0, text_analysis.word_count / 200)
        else:
            content_score = 0.3
        scores.append(content_score)

        # Entity richness
        entity_score = min(1.0, len(text_analysis.entities) / 5)
        scores.append(entity_score)

        # Key phrase richness
        phrase_score = min(1.0, len(text_analysis.key_phrases) / 5)
        scores.append(phrase_score)

        return sum(scores) / len(scores)

    def _calculate_classification_confidence(self, classifications: List[Any]) -> float:
        """Calculate average classification confidence"""
        if not classifications:
            return 0.0

        confidences = [c.confidence_score for c in classifications if hasattr(c, 'confidence_score')]
        return sum(confidences) / len(confidences) if confidences else 0.0

    def _generate_recommendations(
        self,
        extracted_data: ExtractedData,
        text_analysis: TextAnalysis,
        industry_classifications: List[Any],
        validation_report: Any
    ) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []

        # Data completeness recommendations
        if not extracted_data.description or len(extracted_data.description) < 100:
            recommendations.append("Obtain more detailed business description")

        if not extracted_data.industry:
            recommendations.append("Verify industry classification")

        if not extracted_data.headquarters:
            recommendations.append("Confirm headquarters location")

        if not extracted_data.ceo_name:
            recommendations.append("Update current CEO/leadership information")

        # Text quality recommendations
        if text_analysis.readability_score < 0.3:
            recommendations.append("Business description may be too complex - consider simplification")

        if len(text_analysis.entities) < 3:
            recommendations.append("Business description lacks specific details - add more concrete information")

        # Classification recommendations
        if not industry_classifications:
            recommendations.append("Manual industry classification required")
        elif len(industry_classifications) > 1:
            top_confidence = industry_classifications[0].confidence_score
            if top_confidence < 0.7:
                recommendations.append("Industry classification has low confidence - manual review recommended")

        # Validation recommendations
        if validation_report and validation_report.recommendations:
            recommendations.extend(validation_report.recommendations[:3])  # Add top 3 validation recommendations

        return recommendations[:10]  # Limit to top 10

    async def _update_company_record(self, company: Company, extracted_data: ExtractedData, quality_score: float):
        """Update company record with processed data"""
        try:
            with get_db_session() as session:
                company_record = session.query(Company).filter(Company.id == company.id).first()
                if company_record:
                    # Update fields with extracted data
                    if extracted_data.industry and not company_record.industry:
                        company_record.industry = extracted_data.industry

                    if extracted_data.sector and not company_record.sector:
                        company_record.sector = extracted_data.sector

                    if extracted_data.description and (not company_record.description or len(extracted_data.description) > len(company_record.description or "")):
                        company_record.description = extracted_data.description

                    if extracted_data.headquarters and not company_record.headquarters:
                        company_record.headquarters = extracted_data.headquarters

                    if extracted_data.ceo_name and not company_record.ceo_name:
                        company_record.ceo_name = extracted_data.ceo_name

                    if extracted_data.founded_year and not company_record.founded_year:
                        company_record.founded_year = extracted_data.founded_year

                    if extracted_data.employee_count and not company_record.employee_count:
                        company_record.employee_count = extracted_data.employee_count

                    # Update quality metrics
                    company_record.data_quality_score = quality_score
                    company_record.last_enhanced = datetime.utcnow()
                    company_record.enhancement_status = 'completed'

                    session.commit()

        except Exception as e:
            logger.error(f"Failed to update company record: {e}")

    def _convert_source_to_extracted_data(self, source: CompanyDataSource) -> ExtractedData:
        """Convert database source record to ExtractedData object"""
        data = source.data_extracted or {}

        return ExtractedData(
            company_name=data.get('company_name'),
            industry=data.get('industry'),
            sector=data.get('sector'),
            description=data.get('description'),
            website=data.get('website'),
            headquarters=data.get('headquarters'),
            ceo_name=data.get('ceo_name'),
            founded_year=data.get('founded_year'),
            employee_count=data.get('employee_count'),
            source_type=source.source_type,
            source_url=source.source_url,
            confidence_score=source.confidence_score or 0.5,
            keywords=data.get('keywords', [])
        )

    def _load_entity_patterns(self) -> Dict[str, List[str]]:
        """Load entity extraction patterns"""
        return {
            'PERSON': [
                r'CEO\s+([A-Z][a-z]+\s+[A-Z][a-z]+)',
                r'Chief Executive Officer\s+([A-Z][a-z]+\s+[A-Z][a-z]+)',
                r'founded by\s+([A-Z][a-z]+\s+[A-Z][a-z]+)'
            ],
            'LOCATION': [
                r'headquartered in\s+([A-Z][a-z]+(?:,\s*[A-Z][a-z]+)*)',
                r'based in\s+([A-Z][a-z]+(?:,\s*[A-Z][a-z]+)*)'
            ],
            'DATE': [
                r'founded in\s+(\d{4})',
                r'established in\s+(\d{4})',
                r'since\s+(\d{4})'
            ]
        }
