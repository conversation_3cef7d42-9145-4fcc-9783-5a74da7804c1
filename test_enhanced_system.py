#!/usr/bin/env python3
"""
Test script for the Enhanced IPO System
This script tests the basic functionality of the enhanced system components.
"""

import asyncio
import sys
from datetime import datetime

from loguru import logger
from rich.console import Console
from rich.panel import Panel

# Configure logging
logger.remove()
logger.add(sys.stderr, level="INFO")

console = Console()


def test_imports():
    """Test that all modules can be imported successfully"""
    console.print("[bold blue]Testing module imports...[/bold blue]")
    
    try:
        from config import Config
        console.print("✅ Config imported successfully")
        
        from database import get_db_session, init_database
        console.print("✅ Database module imported successfully")
        
        from models import Company, IPO, CompanyDataSource, IndustryClassification, ValidationResult
        console.print("✅ Models imported successfully")
        
        from enhanced_data_collector import MultiSourceDataCollector
        console.print("✅ Enhanced data collector imported successfully")
        
        from website_scraper import CompanyWebsiteScraper
        console.print("✅ Website scraper imported successfully")
        
        from industry_classifier import EnhancedIndustryClassifier
        console.print("✅ Industry classifier imported successfully")
        
        from data_validator import DataValidator
        console.print("✅ Data validator imported successfully")
        
        from data_processing_pipeline import IntelligentDataProcessor
        console.print("✅ Data processing pipeline imported successfully")
        
        from enhanced_ipo_system import EnhancedIPOSystem
        console.print("✅ Enhanced IPO system imported successfully")
        
        return True
        
    except Exception as e:
        console.print(f"❌ Import failed: {e}")
        return False


def test_database_connection():
    """Test database connection and table creation"""
    console.print("\n[bold blue]Testing database connection...[/bold blue]")
    
    try:
        from config import Config
        from database import init_database, get_db_session
        from models import Company
        
        # Validate config
        Config.validate()
        console.print("✅ Configuration validated")
        
        # Initialize database
        init_database()
        console.print("✅ Database initialized successfully")
        
        # Test connection
        with get_db_session() as session:
            count = session.query(Company).count()
            console.print(f"✅ Database connection successful - found {count} companies")
        
        return True
        
    except Exception as e:
        console.print(f"❌ Database test failed: {e}")
        return False


async def test_website_scraper():
    """Test website scraping functionality"""
    console.print("\n[bold blue]Testing website scraper...[/bold blue]")
    
    try:
        from website_scraper import CompanyWebsiteScraper
        
        # Test with a simple website
        test_url = "https://example.com"
        test_company = "Example Company"
        
        async with CompanyWebsiteScraper() as scraper:
            # This will likely fail due to example.com not having company info,
            # but it tests that the scraper can be instantiated and run
            result = await scraper.extract_company_data(test_url, test_company)
            
            if result:
                console.print(f"✅ Website scraper returned data: {result.company_name}")
            else:
                console.print("✅ Website scraper ran successfully (no data extracted from example.com as expected)")
        
        return True
        
    except Exception as e:
        console.print(f"❌ Website scraper test failed: {e}")
        return False


def test_industry_classifier():
    """Test industry classification functionality"""
    console.print("\n[bold blue]Testing industry classifier...[/bold blue]")
    
    try:
        from industry_classifier import EnhancedIndustryClassifier
        from models import Company
        
        classifier = EnhancedIndustryClassifier()
        
        # Create a test company
        test_company = Company()
        test_company.id = 999999  # Use a high ID that won't conflict
        test_company.name = "Test Biotech Company"
        
        # Test classification
        results = classifier.classify_company(
            test_company,
            description="A biotechnology company focused on drug development and clinical trials for cancer therapeutics.",
            website_keywords=["biotechnology", "pharmaceutical", "drug development"]
        )
        
        if results:
            best_result = results[0]
            console.print(f"✅ Industry classifier returned: {best_result.industry} (confidence: {best_result.confidence_score:.3f})")
        else:
            console.print("⚠️ Industry classifier returned no results")
        
        return True
        
    except Exception as e:
        console.print(f"❌ Industry classifier test failed: {e}")
        return False


def test_data_validator():
    """Test data validation functionality"""
    console.print("\n[bold blue]Testing data validator...[/bold blue]")
    
    try:
        from data_validator import DataValidator
        from models import Company
        
        validator = DataValidator()
        
        # Create a test company
        test_company = Company()
        test_company.id = 999999
        test_company.name = "Test Company"
        test_company.industry = "Technology"
        test_company.sector = "Information Technology"
        test_company.description = "A technology company"
        
        # Test validation (will have limited data sources, so expect warnings)
        report = validator.validate_company_data(test_company)
        
        console.print(f"✅ Data validator returned report with {len(report.issues)} issues")
        console.print(f"   Overall score: {report.overall_score:.3f}")
        
        return True
        
    except Exception as e:
        console.print(f"❌ Data validator test failed: {e}")
        return False


async def test_enhanced_system():
    """Test the complete enhanced system"""
    console.print("\n[bold blue]Testing enhanced IPO system...[/bold blue]")
    
    try:
        from enhanced_ipo_system import EnhancedIPOSystem
        
        system = EnhancedIPOSystem()
        
        # Test system status
        summary = system.get_enhancement_summary()
        console.print(f"✅ Enhanced system status: {summary['total_companies']} total companies")
        console.print(f"   Enhanced: {summary['enhanced_companies']}, Failed: {summary['failed_companies']}")
        console.print(f"   Average quality: {summary['average_quality_score']:.3f}")
        
        return True
        
    except Exception as e:
        console.print(f"❌ Enhanced system test failed: {e}")
        return False


async def main():
    """Run all tests"""
    console.print(Panel.fit(
        "[bold green]Enhanced IPO System Test Suite[/bold green]\n"
        "Testing all components of the enhanced IPO data collection system",
        title="🧪 Test Suite"
    ))
    
    tests = [
        ("Module Imports", test_imports),
        ("Database Connection", test_database_connection),
        ("Website Scraper", test_website_scraper),
        ("Industry Classifier", test_industry_classifier),
        ("Data Validator", test_data_validator),
        ("Enhanced System", test_enhanced_system),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        console.print(f"\n{'='*60}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            console.print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    console.print(f"\n{'='*60}")
    console.print("[bold blue]Test Results Summary:[/bold blue]")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        console.print(f"  {status} {test_name}")
    
    console.print(f"\n[bold]Overall: {passed}/{total} tests passed[/bold]")
    
    if passed == total:
        console.print("\n🎉 [bold green]All tests passed! The enhanced IPO system is ready to use.[/bold green]")
        console.print("\nYou can now run:")
        console.print("  [cyan]python run_enhanced_system.py status[/cyan] - Check system status")
        console.print("  [cyan]python run_enhanced_system.py list[/cyan] - List companies")
        console.print("  [cyan]python run_enhanced_system.py enhance-all[/cyan] - Enhance all companies")
    else:
        console.print(f"\n⚠️ [bold yellow]{total - passed} tests failed. Please check the errors above.[/bold yellow]")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
