"""
Clean database and verify no mock/incorrect data exists
"""

import sys
from database import get_db_session, init_database
from models import Company, IPO
from config import Config

def clean_database():
    """Remove any existing data and start fresh"""
    print("🧹 Cleaning database...")
    
    try:
        with get_db_session() as session:
            # Delete all IPOs first (due to foreign key constraints)
            ipo_count = session.query(IPO).count()
            if ipo_count > 0:
                session.query(IPO).delete()
                print(f"   Deleted {ipo_count} IPO records")
            
            # Delete all companies
            company_count = session.query(Company).count()
            if company_count > 0:
                session.query(Company).delete()
                print(f"   Deleted {company_count} company records")
            
            session.commit()
            print("✅ Database cleaned successfully")
            return True
            
    except Exception as e:
        print(f"❌ Failed to clean database: {e}")
        return False

def verify_clean_database():
    """Verify database is clean"""
    print("\n🔍 Verifying database is clean...")
    
    try:
        with get_db_session() as session:
            company_count = session.query(Company).count()
            ipo_count = session.query(IPO).count()
            
            print(f"   Companies: {company_count}")
            print(f"   IPOs: {ipo_count}")
            
            if company_count == 0 and ipo_count == 0:
                print("✅ Database is clean")
                return True
            else:
                print("❌ Database still contains data")
                return False
                
    except Exception as e:
        print(f"❌ Failed to verify database: {e}")
        return False

def verify_configuration():
    """Verify configuration is correct"""
    print("\n⚙️  Verifying configuration...")
    
    print(f"   USE_MOCK_DATA: {Config.USE_MOCK_DATA}")
    print(f"   Database URL: {Config.DATABASE_URL}")
    print(f"   OpenAI API Key: {'Set' if Config.OPENAI_API_KEY else 'Not set'}")
    print(f"   Alpha Vantage Key: {'Set' if Config.ALPHA_VANTAGE_API_KEY else 'Not set'}")
    
    if not Config.USE_MOCK_DATA:
        print("✅ Mock data is disabled - system will not add fake companies")
    else:
        print("⚠️  Mock data is enabled - system will add test companies")
    
    return True

def test_data_collection_with_clean_state():
    """Test data collection to ensure no mock data is added"""
    print("\n🧪 Testing data collection with clean state...")
    
    try:
        from data_collectors import IPODataManager
        
        data_manager = IPODataManager()
        new_count = data_manager.collect_new_ipos()
        
        print(f"   Collection result: {new_count} new IPOs")
        
        # Verify no data was added
        with get_db_session() as session:
            company_count = session.query(Company).count()
            ipo_count = session.query(IPO).count()
            
            if company_count == 0 and ipo_count == 0:
                print("✅ No mock data was added - system is working correctly")
                return True
            else:
                print(f"❌ Unexpected data was added: {company_count} companies, {ipo_count} IPOs")
                
                # Show what was added
                companies = session.query(Company).all()
                for company in companies:
                    print(f"   - {company.name}")
                
                return False
                
    except Exception as e:
        print(f"❌ Data collection test failed: {e}")
        return False

def main():
    """Main cleanup and verification process"""
    print("🚀 IPO TRACKER DATABASE CLEANUP & VERIFICATION")
    print("=" * 60)
    
    steps = [
        ("Clean Database", clean_database),
        ("Verify Clean State", verify_clean_database),
        ("Verify Configuration", verify_configuration),
        ("Test Data Collection", test_data_collection_with_clean_state),
    ]
    
    passed = 0
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            if step_func():
                passed += 1
                print(f"✅ {step_name}: PASSED")
            else:
                print(f"❌ {step_name}: FAILED")
        except Exception as e:
            print(f"❌ {step_name}: CRASHED - {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 Results: {passed}/{len(steps)} steps passed")
    
    if passed == len(steps):
        print("\n🎉 Database is clean and system is properly configured!")
        print("\nYour IPO tracker is now ready for production use:")
        print("✅ No mock/fake data in database")
        print("✅ Mock data generation is disabled")
        print("✅ System will only add real IPO data when available")
        print("✅ Ready for real SEC EDGAR integration")
        
        print("\nNext steps:")
        print("1. The system is now clean and ready")
        print("2. When real SEC API is implemented, it will add genuine data")
        print("3. You can safely run the background scheduler")
        print("4. No fake companies will be added to your database")
    else:
        print(f"\n⚠️  {len(steps) - passed} step(s) failed")
        print("Please review the errors above")
    
    return passed == len(steps)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
