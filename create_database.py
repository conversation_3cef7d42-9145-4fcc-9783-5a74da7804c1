"""
Simple script to create the IPO tracker database.
This will connect to PostgreSQL and create the database if it doesn't exist.
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from config import Config
import sys

def create_database():
    """Create the ipo_tracker database if it doesn't exist"""
    
    # Parse the database URL to get connection details
    db_url = Config.DATABASE_URL
    print(f"Using database URL: {db_url}")
    
    # Extract connection details
    # Format: postgresql://username:password@localhost:5432/database_name
    if "postgresql://" in db_url:
        parts = db_url.replace("postgresql://", "").split("/")
        user_host_port = parts[0]
        database_name = parts[1] if len(parts) > 1 else "ipo_tracker"

        if "@" in user_host_port:
            user_pass, host_port = user_host_port.split("@")
            if ":" in user_pass:
                user, password = user_pass.split(":", 1)  # Split only on first colon
            else:
                user = user_pass
                password = None
        else:
            user = "zhouchaoran"  # default
            password = None
            host_port = user_host_port

        if ":" in host_port:
            host, port = host_port.split(":")
        else:
            host = host_port
            port = "5432"
    else:
        print("❌ Invalid database URL format")
        return False
    
    print(f"Connecting as user: {user}")
    print(f"Host: {host}, Port: {port}")
    print(f"Target database: {database_name}")
    print(f"Password: {'Set' if password else 'Not set'}")

    try:
        # First, connect to the default 'postgres' database to create our database
        print("\n🔄 Connecting to PostgreSQL...")
        conn_params = {
            "host": host,
            "port": port,
            "user": user,
            "database": "postgres"  # Connect to default database first
        }
        if password:
            conn_params["password"] = password

        conn = psycopg2.connect(**conn_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        print("✅ Connected to PostgreSQL successfully!")
        
        # Check if database exists
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (database_name,))
        exists = cursor.fetchone()
        
        if exists:
            print(f"✅ Database '{database_name}' already exists")
        else:
            # Create the database
            print(f"🔄 Creating database '{database_name}'...")
            cursor.execute(f'CREATE DATABASE "{database_name}"')
            print(f"✅ Database '{database_name}' created successfully!")
        
        cursor.close()
        conn.close()
        
        # Now test connection to our new database
        print(f"\n🔄 Testing connection to '{database_name}'...")
        test_conn_params = {
            "host": host,
            "port": port,
            "user": user,
            "database": database_name
        }
        if password:
            test_conn_params["password"] = password

        test_conn = psycopg2.connect(**test_conn_params)
        test_cursor = test_conn.cursor()
        test_cursor.execute("SELECT version();")
        version = test_cursor.fetchone()
        print(f"✅ Connected to {database_name} successfully!")
        print(f"   PostgreSQL version: {version[0]}")
        
        test_cursor.close()
        test_conn.close()
        
        return True
        
    except psycopg2.Error as e:
        print(f"❌ PostgreSQL error: {e}")
        print("\nTroubleshooting tips:")
        print("1. Make sure Postgres.app is running")
        print("2. Check that the server is listening on localhost:5432")
        print("3. Verify your username in the connection string")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🗄️ IPO Tracker Database Setup")
    print("=" * 40)
    
    success = create_database()
    
    if success:
        print("\n🎉 Database setup completed successfully!")
        print("You can now run: python test_setup.py")
    else:
        print("\n❌ Database setup failed. Please check the errors above.")
    
    sys.exit(0 if success else 1)
