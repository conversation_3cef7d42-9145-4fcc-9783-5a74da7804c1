"""
Configuration settings for the IPO Tracker application.
This file manages all the settings and environment variables.
"""

import os
from pathlib import Path
from typing import List, Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Application configuration class"""

    # Database settings
    DATABASE_URL: str = os.getenv('DATABASE_URL', 'postgresql://localhost:5432/ipo_tracker')

    # API Keys
    OPENAI_API_KEY: Optional[str] = os.getenv('OPENAI_API_KEY')
    ALPHA_VANTAGE_API_KEY: Optional[str] = os.getenv('ALPHA_VANTAGE_API_KEY')

    # Application settings
    LOG_LEVEL: str = os.getenv('LOG_LEVEL', 'INFO')
    SCHEDULER_INTERVAL_HOURS: int = int(os.getenv('SCHEDULER_INTERVAL_HOURS', '6'))
    
    # Directories
    BASE_DIR: Path = Path(__file__).parent
    LOGS_DIR: Path = BASE_DIR / 'logs'
    DATA_DIR: Path = BASE_DIR / 'data'

    # Create directories if they don't exist
    LOGS_DIR.mkdir(exist_ok=True)
    DATA_DIR.mkdir(exist_ok=True)

    # API Rate Limits (requests per minute)
    SEC_RATE_LIMIT: int = 10
    ALPHA_VANTAGE_RATE_LIMIT: int = 5

    # Development settings
    USE_MOCK_DATA: bool = os.getenv('USE_MOCK_DATA', 'false').lower() == 'true'

    @classmethod
    def validate(cls) -> bool:
        """Validate that required configuration is present"""
        missing: List[str] = []
        
        if not cls.OPENAI_API_KEY:
            missing.append('OPENAI_API_KEY')
        
        if missing:
            raise ValueError(f"Missing required environment variables: {', '.join(missing)}")
        
        return True
