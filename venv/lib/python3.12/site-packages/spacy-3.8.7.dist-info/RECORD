../../../bin/spacy,sha256=6dICl8nIia5G5lPO3h9yg70sq9-gEKpdDtbVAB_ar0g,256
spacy-3.8.7.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
spacy-3.8.7.dist-info/METADATA,sha256=9Yv5tb5scFDMvkByNiG-gZ7AhnbE6vk8YnVyupWsLYo,27989
spacy-3.8.7.dist-info/RECORD,,
spacy-3.8.7.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy-3.8.7.dist-info/WHEEL,sha256=4L-EWmFVbI0pXYjNz6IJeqY_rXC1bfF8QrwnNh6BsTs,109
spacy-3.8.7.dist-info/entry_points.txt,sha256=q6IIfdWIqV5G_nzLChejIJpbzXNEg4iqHL05cTBO888,46
spacy-3.8.7.dist-info/licenses/LICENSE,sha256=P0N45ncI_srNPidlHIvA-XfPIHieGdoNgaKEDnz1c6I,1128
spacy-3.8.7.dist-info/top_level.txt,sha256=oXTJOtzJdUdzkIbih8SAC-lK9JfD0jvZ_43H5fmTCHw,6
spacy/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/__init__.py,sha256=5idCu6y8Fid7CteAdVBhvvg4XRW1cGxKoBUyJ3iaGbA,2996
spacy/__main__.py,sha256=aYNzgJwgtrUCb9acqibEj5v1eQXQ_3AFhQKaDPsx4Js,80
spacy/__pycache__/__init__.cpython-312.pyc,,
spacy/__pycache__/__main__.cpython-312.pyc,,
spacy/__pycache__/about.cpython-312.pyc,,
spacy/__pycache__/compat.cpython-312.pyc,,
spacy/__pycache__/errors.cpython-312.pyc,,
spacy/__pycache__/git_info.cpython-312.pyc,,
spacy/__pycache__/glossary.cpython-312.pyc,,
spacy/__pycache__/language.cpython-312.pyc,,
spacy/__pycache__/lookups.cpython-312.pyc,,
spacy/__pycache__/pipe_analysis.cpython-312.pyc,,
spacy/__pycache__/registrations.cpython-312.pyc,,
spacy/__pycache__/schemas.cpython-312.pyc,,
spacy/__pycache__/scorer.cpython-312.pyc,,
spacy/__pycache__/ty.cpython-312.pyc,,
spacy/__pycache__/util.cpython-312.pyc,,
spacy/about.py,sha256=-HINc8JKNACaJ4ZJJnYrzxUB6K3qU3ep6JBjr3FgZu8,239
spacy/attrs.cpython-312-darwin.so,sha256=iahpmKTtfM8JewUU1UJepPXPGPrvsuSa3-Jp-SZOKV0,110552
spacy/attrs.pxd,sha256=xPs5g83G2_vC3BgLqXUNpn4-NhM3kycFg22rGd8HHH4,1157
spacy/attrs.pyx,sha256=shu88i6hC5Z0gPV0NaxEoR0ZPpNBeahBZE3keFoWmnk,5790
spacy/cli/__init__.py,sha256=qtczRmrgx2vbUZl1pRV4BsoabdRWAS7_kxerCqCAiLw,2399
spacy/cli/__pycache__/__init__.cpython-312.pyc,,
spacy/cli/__pycache__/_util.cpython-312.pyc,,
spacy/cli/__pycache__/apply.cpython-312.pyc,,
spacy/cli/__pycache__/assemble.cpython-312.pyc,,
spacy/cli/__pycache__/benchmark_speed.cpython-312.pyc,,
spacy/cli/__pycache__/convert.cpython-312.pyc,,
spacy/cli/__pycache__/debug_config.cpython-312.pyc,,
spacy/cli/__pycache__/debug_data.cpython-312.pyc,,
spacy/cli/__pycache__/debug_diff.cpython-312.pyc,,
spacy/cli/__pycache__/debug_model.cpython-312.pyc,,
spacy/cli/__pycache__/download.cpython-312.pyc,,
spacy/cli/__pycache__/evaluate.cpython-312.pyc,,
spacy/cli/__pycache__/find_function.cpython-312.pyc,,
spacy/cli/__pycache__/find_threshold.cpython-312.pyc,,
spacy/cli/__pycache__/info.cpython-312.pyc,,
spacy/cli/__pycache__/init_config.cpython-312.pyc,,
spacy/cli/__pycache__/init_pipeline.cpython-312.pyc,,
spacy/cli/__pycache__/package.cpython-312.pyc,,
spacy/cli/__pycache__/pretrain.cpython-312.pyc,,
spacy/cli/__pycache__/profile.cpython-312.pyc,,
spacy/cli/__pycache__/train.cpython-312.pyc,,
spacy/cli/__pycache__/validate.cpython-312.pyc,,
spacy/cli/_util.py,sha256=khGzQYb3FAMJ4xcIlnsiWq6VzO6PnU-xmDFG6-wF7ZA,10860
spacy/cli/apply.py,sha256=i8hbowhunlixTjBTFUccIEeGgORouv7h4B5f0JLJEn0,4870
spacy/cli/assemble.py,sha256=zsNfeVqmQRj6S5IhhGLEA82JpxP_xxrmyxZH78ZIo5Q,2577
spacy/cli/benchmark_speed.py,sha256=d-vkAJDBSiypiBpq_E0kDTFaIavn3VxOYTe_2cKC3hI,5442
spacy/cli/convert.py,sha256=ftFifaJrB8XuN02Zg6-4wV84OzvCn33PgIlbsfufnec,9389
spacy/cli/debug_config.py,sha256=KfxlQ7WElgaI27N2dULm6xJcvqK0PF_iot6XDvkuY_k,4558
spacy/cli/debug_data.py,sha256=QW8EIbqdVaB_6lEqxZruO_LsA4Z-5DXJiacv4xaYrpo,50253
spacy/cli/debug_diff.py,sha256=QZPaCoee7c_qyF1ovZ6_x5kOX-NqVtGTzThCRQj5pqA,3580
spacy/cli/debug_model.py,sha256=uSaoqWaH1H7YIrZekwD7AqdjovTP3B58mrgshxp7-YM,8929
spacy/cli/download.py,sha256=jsgFFr_0aYz20jd5QVW51djbtNNqm_ByHYCkK3BrMMc,6423
spacy/cli/evaluate.py,sha256=luRAE29avW7j5BNA8WKcjUrBHUEC3O4iNfjSK5b9RIc,9408
spacy/cli/find_function.py,sha256=sBN1iXrsdtEdYcMdrQAA-MOlUsYd1vLjhDhiFjcXuwQ,2122
spacy/cli/find_threshold.py,sha256=IqER5dLQub8Yt9laLNeE9vlde71w07WHNUV5D0MRUr4,9372
spacy/cli/info.py,sha256=pibnk8HTw1jSk9eXnM32cz6jTkJwNRJAKPXq-YOR9jk,6330
spacy/cli/init_config.py,sha256=DX1ZxnQCC8hu2sLcnR7uagfAcq8z5SJFN7-pD5MGYpY,10391
spacy/cli/init_pipeline.py,sha256=zjp6-_HpFxcB0j53cs7b8h-hUtR1JCUlALg3OEOxHe4,5769
spacy/cli/package.py,sha256=AecXJq0t6qsRITJ4K7-waq3g4u8E6ZpH1-b8AmnXHjM,23868
spacy/cli/pretrain.py,sha256=kmXBE2fQAnVTehKrPODMUUAKILdFrDFAxjbDJwIMhzw,5276
spacy/cli/profile.py,sha256=sRRwk3SagJaStUGbCsqIITdpsl-7wnGdFZrxsFx3aRk,3467
spacy/cli/project/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/cli/project/__pycache__/__init__.cpython-312.pyc,,
spacy/cli/project/__pycache__/assets.cpython-312.pyc,,
spacy/cli/project/__pycache__/clone.cpython-312.pyc,,
spacy/cli/project/__pycache__/document.cpython-312.pyc,,
spacy/cli/project/__pycache__/dvc.cpython-312.pyc,,
spacy/cli/project/__pycache__/pull.cpython-312.pyc,,
spacy/cli/project/__pycache__/push.cpython-312.pyc,,
spacy/cli/project/__pycache__/remote_storage.cpython-312.pyc,,
spacy/cli/project/__pycache__/run.cpython-312.pyc,,
spacy/cli/project/assets.py,sha256=QhqDFKS96M2VUuWSpzZ2XakRNatCBqCPrzeyLR-te58,32
spacy/cli/project/clone.py,sha256=T_XUBzheRtu6R9vGjjnnhfTJC6eQ0fuqIq5EMa03pM0,31
spacy/cli/project/document.py,sha256=vcm2KNtP4GuoPzxvy6VFB1iNCJpCHPvpfg5wpavqkHM,34
spacy/cli/project/dvc.py,sha256=v4gGJ89ON7cN35ilscPPo1lc9Xw337-agOtL04n_1Po,29
spacy/cli/project/pull.py,sha256=c-Bt1u9abcnHt-6wW27XFOekXBEa2dUZnax7ffhLzdA,30
spacy/cli/project/push.py,sha256=YxzRHHR4nJn8Z4YN7YRhyHntCbx5zHxbUtW8Ki_l8w4,30
spacy/cli/project/remote_storage.py,sha256=cW3IAB5gDvEee4RP3iyP5gbwT2jPeNms4bWS0MpL_wA,40
spacy/cli/project/run.py,sha256=BDSssXsguV6vxMjfTx7Ikg7WEDUlrqatB_FMk6uaNEE,29
spacy/cli/templates/quickstart_training.jinja,sha256=_7tLWpB3Og-hS7XI9c6rXrr6p5f4O-EPiJkpnmZsJy8,16352
spacy/cli/templates/quickstart_training_recommendations.yml,sha256=O-o3n8q6X9tQTh2A2aHAom6dxVSyEYXZ5_ysyIdObYY,6294
spacy/cli/train.py,sha256=SgyVMzE7BtEmbogZL-tiUs9GIedA0NAuSU_FQsMiCMk,3406
spacy/cli/validate.py,sha256=qgQRHVRI0len03-MWGt3N_kJaSUuFnbmXv8lsmfJTNg,4587
spacy/compat.py,sha256=PufY29SEqRSuRq0WmnFrwoyr6epe1avDL0aA3vDfoxU,1347
spacy/default_config.cfg,sha256=pE57YQJ6qCxl-zP4OT5DCq-sI2Nyg61Jv6OsD6DzlxA,4395
spacy/default_config_pretraining.cfg,sha256=fcLb8s1SLIrDk_cOx1XWK-ku8v06JiIpVsvAkLKVwrY,738
spacy/displacy/__init__.py,sha256=ohYw4lT1W1Bewsu1WIlNmU5arubDBA9d_UyMR6msZ4k,10337
spacy/displacy/__pycache__/__init__.cpython-312.pyc,,
spacy/displacy/__pycache__/render.cpython-312.pyc,,
spacy/displacy/__pycache__/templates.cpython-312.pyc,,
spacy/displacy/render.py,sha256=hN-j_qDHr9iOB1czyTttd9iGOfCBuOjKNSKFIR5VOi4,24699
spacy/displacy/templates.py,sha256=wPMoTcIavVakLqcyeBfWZq-qqWEBwLH8wmTFjHxdgSc,4656
spacy/errors.py,sha256=zxCC3xzUQ9d_RSUa_a6ul7irYus3xIc93bMvnBRTB28,68134
spacy/git_info.py,sha256=KV88qEsDV5SaQdNucvZsf1z6ACyhoYIxVGkiiVhQb7E,71
spacy/glossary.py,sha256=MGpsfggbSziXf4mKMiofzgydGmTTr2p9DFCIoQf3hok,13186
spacy/kb/__init__.py,sha256=NmeSh5Dowl0A2A472qoNhT0P7YAW7TCgpyqkUmUZnmE,271
spacy/kb/__pycache__/__init__.cpython-312.pyc,,
spacy/kb/candidate.cpython-312-darwin.so,sha256=fG19PN9ptnG_8fx3sN8qBjiPdlbYwDG_bVVmuK4r210,131520
spacy/kb/candidate.pxd,sha256=BVG2fxl364mPHMlLVe9nOR_egGHapQHZLkEOmoDlQfw,394
spacy/kb/candidate.pyx,sha256=-Jk39ocqQNsI4NjzWfg0kSU1Q_32QtkgWJh0jmKkg9s,2679
spacy/kb/kb.cpython-312-darwin.so,sha256=ouiBmeJsL9v3RnOCPrCuafL2TOXpwxoeZS_DwDr8LUc,131592
spacy/kb/kb.pxd,sha256=nZuZcmRswLd7a8mAwRvUDExNORrBxfWQPHUXCRYsQJI,267
spacy/kb/kb.pyx,sha256=MNFrbP5wqQcfSq6eCPmokxjztNXFJtON39WBtUKJHLA,4697
spacy/kb/kb_in_memory.cpython-312-darwin.so,sha256=I8ka47j0h3oaYChLmlJglIz2F7c5CVfGpJSInfUzMBY,350032
spacy/kb/kb_in_memory.pxd,sha256=ZkJPn8KKVADM03W3EKsrcmNATpM4MGDBmc7WkenHkfw,6961
spacy/kb/kb_in_memory.pyx,sha256=H-r6gAnmfrSbFPwY77rGSXSBqQgzhjtLXOXRu9Av8-c,27123
spacy/lang/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/lang/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/__pycache__/char_classes.cpython-312.pyc,,
spacy/lang/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/__pycache__/norm_exceptions.cpython-312.pyc,,
spacy/lang/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/af/__init__.py,sha256=ndLqoYkBJbIScOjRXjdVfBu-knKOINfLIr0mpt2yRJ8,255
spacy/lang/af/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/af/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/af/stop_words.py,sha256=iRVnsSz52K1n0hV6IdLa2gqxaVjBEt1ZJBUK7lcQ5EU,291
spacy/lang/am/__init__.py,sha256=oriz_EsKwDmk-c-SjyVjYANKTtfsdyR5a5WHi5rNp18,830
spacy/lang/am/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/am/__pycache__/examples.cpython-312.pyc,,
spacy/lang/am/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/am/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/am/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/am/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/am/examples.py,sha256=pKe5F6MKlcbOMCgtlDFA5wjmIH6AJYVmsTtvZlKrYPc,792
spacy/lang/am/lex_attrs.py,sha256=JP9c06pnU60VZtqv0GANirrdLgcv2fFYTkOhcFLBMXA,2192
spacy/lang/am/punctuation.py,sha256=5xNH7XLiwqzZJD4paOHYzDavnv4HqxGWTynMwL9t__o,547
spacy/lang/am/stop_words.py,sha256=uh4fQMwU_MIb68IondDPf669I3rryUk0mVB5FosOjT4,3202
spacy/lang/am/tokenizer_exceptions.py,sha256=HiNnzuKBWhQ_GvoRV8ax3BTNFH3zASxQyoRjpD5jx4E,290
spacy/lang/ar/__init__.py,sha256=tEGwfjczDjZbSeBANcQDqGhWwUgf1RZjmctxonlSxgU,572
spacy/lang/ar/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ar/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ar/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ar/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ar/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ar/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ar/examples.py,sha256=DkMnCDqHzPsvxUN-_6u32OBAMzGu3RKk3wmgkT79IKg,890
spacy/lang/ar/lex_attrs.py,sha256=srUxO7oJtSMxHpSOuVQnaFirl2G2TVVuD8U24q66bSg,1309
spacy/lang/ar/punctuation.py,sha256=_97MwjZeLGRGuCIPGSHR8ZSk-ake8aKP_0r-lJKVEg8,463
spacy/lang/ar/stop_words.py,sha256=8tyF0J384bSDuqWwqhz5i1b9ppdITtVfBE3uWuwqJuQ,3180
spacy/lang/ar/tokenizer_exceptions.py,sha256=vDiJzIdWCJD0JkWpbL145Mwimaeh6OvciYHSe23F-3Q,1501
spacy/lang/az/__init__.py,sha256=Pcf91ANTGf4tqXOe8GJITH7Lkq_cPbSv1qx6LcVPfZ4,329
spacy/lang/az/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/az/__pycache__/examples.cpython-312.pyc,,
spacy/lang/az/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/az/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/az/examples.py,sha256=6vpsvY3f0fWW9eovHKAUhXrY-1NX7di6dXj0v4rS6do,747
spacy/lang/az/lex_attrs.py,sha256=IA32skqW6rbG_GA28mwZhepn8a7Qj4ANxjajAcKh-9M,1696
spacy/lang/az/stop_words.py,sha256=E1UTxMlumEGTlxQRwqb91iFz69JS9BaYePXiKsg9KRg,966
spacy/lang/bg/__init__.py,sha256=LGJo4qeCmvAoGjuJWgrB4iIem1qqiDUcvsKFPtCd8lw,907
spacy/lang/bg/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/bg/__pycache__/examples.cpython-312.pyc,,
spacy/lang/bg/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/bg/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/bg/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/bg/examples.py,sha256=EvcgN4euQh6eAv0QhNURfk9fN9Me1rT8s08e_ivoNZ0,636
spacy/lang/bg/lex_attrs.py,sha256=4DTv4Z5IjNroMGzC4wKllHL0_FbAo404cBjgkfUHJ8Q,2047
spacy/lang/bg/stop_words.py,sha256=aljLavhUH7elYr_YDdLXh3dHDY_Emiq84vLBQ5FR_c0,4748
spacy/lang/bg/tokenizer_exceptions.py,sha256=X-ug8pqRbbuvmXgENdSR6XV-fh0WM0DSfiBH9DlGE28,9109
spacy/lang/bn/__init__.py,sha256=1MT-FgkOy0i_pSahLQ0IWIWYzGUF3SAXcdH0JCsN3S8,1177
spacy/lang/bn/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/bn/__pycache__/examples.cpython-312.pyc,,
spacy/lang/bn/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/bn/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/bn/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/bn/examples.py,sha256=wRyGe1N78GAImPfGG8KFfkkrY4cQZ7VQKS_1sgqanow,305
spacy/lang/bn/punctuation.py,sha256=9W0EBsml9kdUhqSRcNTPimX_UkGFsR0Ph2P7fxWjuAY,1284
spacy/lang/bn/stop_words.py,sha256=0Z2wbah-AXELZU41-V5TY_kE7O3sJ_CsrG33mvU7HPQ,6156
spacy/lang/bn/tokenizer_exceptions.py,sha256=8EIoy0s2ja73dyvEOVMKuKDO1xxU6VKfgP12IAgDV-k,970
spacy/lang/bo/__init__.py,sha256=wweQecLkKx_x3_QBJlKcNUNpMeQJGf3wkXBDRBmMpHg,313
spacy/lang/bo/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/bo/__pycache__/examples.cpython-312.pyc,,
spacy/lang/bo/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/bo/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/bo/examples.py,sha256=3AfAVWvo7MxgAAlhBDe4Aarl4SJvQa3nwohL3qPDk5I,1641
spacy/lang/bo/lex_attrs.py,sha256=y_iiHJZwqkFBQyFtc6hHtUcBMQ-X_FUge9zmWq8wsc4,1694
spacy/lang/bo/stop_words.py,sha256=6SkQpeJ2Qyq5qs3Dh3_diZLokcEx2dMewihW1AGcOLU,2199
spacy/lang/ca/__init__.py,sha256=auOcGsthXFuz2fsSPBrM_DpFwXy0xfZJjpubrnnB41M,1344
spacy/lang/ca/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ca/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ca/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/ca/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ca/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ca/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ca/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/ca/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ca/examples.py,sha256=y0DMYylMOJCi9Sld9E4n-MVmr8Matz9oetY9kbpJ000,595
spacy/lang/ca/lemmatizer.py,sha256=73UmvqqJMTGdm4GWPRGE68VuU97Vr7OwziGbpC-9z-Y,2843
spacy/lang/ca/lex_attrs.py,sha256=1BvjOaZ4toVd9vYfcNF4aA4Kr1ja7Ip16kMESPa4_Es,955
spacy/lang/ca/punctuation.py,sha256=FHS63TLtVpcLmoHLwlseiJUtlHRipkR9GQ2c2ZEOu5k,1602
spacy/lang/ca/stop_words.py,sha256=OpO3f2StOS-U1Tx5mhv5wLlzQfhZztPkaLXKV6HVxsg,1619
spacy/lang/ca/syntax_iterators.py,sha256=jts-bL3UQJOjYGo1_ka1BB3b8S1nii8g0GrTN0LKjME,1995
spacy/lang/ca/tokenizer_exceptions.py,sha256=dirxHnKyjCfZA0drxIy5Fv9rhd3vC41Cjn7h6710jyg,1961
spacy/lang/char_classes.py,sha256=gkl1L_-QdcK_pqHX5X55wvhZdOpeKLMQgqyOXwQP9bg,14678
spacy/lang/cs/__init__.py,sha256=c8sy6EaJgQxmy0ZsFyvvjmafxkL7SfBikfX6MWH1waE,305
spacy/lang/cs/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/cs/__pycache__/examples.cpython-312.pyc,,
spacy/lang/cs/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/cs/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/cs/examples.py,sha256=fI9osjCPQKUdlQrU1nlfGvBY2c-aWIJCvyfD35dBh8k,1514
spacy/lang/cs/lex_attrs.py,sha256=s_0iG8JXGfnz9vLzgGvcYOynK5DGVdQUN9RDwByeF2U,1074
spacy/lang/cs/stop_words.py,sha256=5QXRFmlKeUbY2nN30pNure2_FPWbbYkElqTVwQXn8I0,2263
spacy/lang/da/__init__.py,sha256=g_SYuI19Zi7XqbPVVPKTBC71BYYQIYPsHBvTRJivJ-E,628
spacy/lang/da/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/da/__pycache__/examples.cpython-312.pyc,,
spacy/lang/da/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/da/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/da/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/da/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/da/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/da/examples.py,sha256=rOzytzscJRJbp8hL6dgc15p1kuUojxDvCYrs0VwzCWU,568
spacy/lang/da/lex_attrs.py,sha256=8xTkQJBAkvRbVByx6C3nTLT5F-NNQJN-5Tr5ywWR1LU,3574
spacy/lang/da/punctuation.py,sha256=rQRIb-CVSRbTzEhLjEYFdBerUTGajQh6NHthl1UFYeA,914
spacy/lang/da/stop_words.py,sha256=JEHfTpzv2E91PjTab4BIbIYnVi9Ga4XUu5m-P5XI0fA,1345
spacy/lang/da/syntax_iterators.py,sha256=LtZvFL2FBscUvs9TgOy80pnh23Yc4R7ajGhdWhw-2vw,2189
spacy/lang/da/tokenizer_exceptions.py,sha256=4Y_OYGSR91EaLCbYjomk3Iv97iBcK0gOmVUk8kUdqPs,8955
spacy/lang/de/__init__.py,sha256=oNlxwDRbKLUaLN9fyBQdf7pi2TbxuQ0Yexn3LPWd4xU,616
spacy/lang/de/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/de/__pycache__/examples.cpython-312.pyc,,
spacy/lang/de/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/de/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/de/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/de/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/de/examples.py,sha256=Lv6D42-Vl9_Ln8ipJqtzdfc9y60CP71y3r5_gmyiAT8,679
spacy/lang/de/punctuation.py,sha256=13Zdq5sWE24Y93Fo2ggSC-UA2lZTxBDv0EuX8oJUcDE,1409
spacy/lang/de/stop_words.py,sha256=Gze-NblDMWqOPawBap-7nF_qDAifdLRdDZlPiGdtbHM,3661
spacy/lang/de/syntax_iterators.py,sha256=LUKaMW4CzsNhz3mHz1-kiZL5Rc9wxCMYLKa_jxP35TY,1850
spacy/lang/de/tokenizer_exceptions.py,sha256=ZKcYLmyXkW3y5-MB6EpUYl1kXSQODJmtwvE4M2zA5eM,5888
spacy/lang/dsb/__init__.py,sha256=1wvSJZq9WBBezGqHwoZ862YdrqIi-_mXpb3VCVUVhWo,334
spacy/lang/dsb/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/dsb/__pycache__/examples.cpython-312.pyc,,
spacy/lang/dsb/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/dsb/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/dsb/examples.py,sha256=C-Rf_0fEClW2bAicPWhxZhQ4WgV2GVd-oc4g9WiwVXg,553
spacy/lang/dsb/lex_attrs.py,sha256=qGjiBuAZuD_XwVSJPxOoATvfkd91GBkjjVdY07W8DFk,1906
spacy/lang/dsb/stop_words.py,sha256=smkHR-e9uxk_7mJ6guPYjunx9bK7b3SeiAYIoQqyExU,118
spacy/lang/el/__init__.py,sha256=hxgJCQQqXfVoCMa4p0TlkfM1CoKPMGyDOvVY9IpDdg0,1330
spacy/lang/el/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/el/__pycache__/examples.cpython-312.pyc,,
spacy/lang/el/__pycache__/get_pos_from_wiktionary.cpython-312.pyc,,
spacy/lang/el/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/el/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/el/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/el/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/el/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/el/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/el/examples.py,sha256=PiI2ldVSnp9tQ3Ex209X_VsCZE6NztkSkivyLNy_kCs,1972
spacy/lang/el/get_pos_from_wiktionary.py,sha256=AtJ-DzeFiTM5SLt6vTNBO8dPxIZGm3J6MYqnw8nVkOE,2087
spacy/lang/el/lemmatizer.py,sha256=31vx4cqi7DWu-ll4eZ4HPn2CVfBrTYFegybvs8T3X1E,2195
spacy/lang/el/lex_attrs.py,sha256=pOl_W1PRQlugN9U65GZ9fqqODp7dMJkPpeCowFQrOIw,2320
spacy/lang/el/punctuation.py,sha256=FoZ9INVWSh3thVPLh-WKqlscISUS0neWN2mQnRJCaKo,3408
spacy/lang/el/stop_words.py,sha256=TISUSnaHPCl--LvIq282b5Q_MtQ7SvN9k0kJ-YcPkbs,8100
spacy/lang/el/syntax_iterators.py,sha256=lsiDWwRRqMd4wppWXmjQNAf7MxdR6mE77_zG4xUoxNY,2290
spacy/lang/el/tokenizer_exceptions.py,sha256=SfkmK25oMTd1WsZAsgsuVHT9oJsW2bQwX88uyp2lBq4,10077
spacy/lang/en/__init__.py,sha256=1smnlEpyKAxtXxwy2z1RjFUVtUxUi3yfo-11cDnrdbA,1236
spacy/lang/en/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/en/__pycache__/examples.cpython-312.pyc,,
spacy/lang/en/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/en/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/en/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/en/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/en/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/en/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/en/examples.py,sha256=__BNRZWT7H0x3quZq0ce0xYgGZU3Lw4L9TwgJxQRrJc,556
spacy/lang/en/lemmatizer.py,sha256=EQaiJ_QDdWFDrHxnFCJCpCAoQPWR5_dHjypQEvgGDrk,1480
spacy/lang/en/lex_attrs.py,sha256=hbDicVwoDOcWwaDXfv7V8Q7o1-dJWJ9hbTyB4bjGH9c,1770
spacy/lang/en/punctuation.py,sha256=_SsU_xmi5_kDghJv40E4HLtUhr8jSItD_i9UI6E_KJM,576
spacy/lang/en/stop_words.py,sha256=Mv97AZ2k2mG0qpepCm4362g6QyXQ0RonsonYsOaVWMo,2148
spacy/lang/en/syntax_iterators.py,sha256=_sFr5EfSoNcnAA9FcpP1aWFM6eBqyWNyvKHdo_60vT8,1570
spacy/lang/en/tokenizer_exceptions.py,sha256=BnZ7pn99ynDWBoy2PYZpuGXQ8GReVBmQ_VEp_0DhQlk,14252
spacy/lang/es/__init__.py,sha256=z6203ypKlkU6jfcM_SmTtrkSnm096D-CUitmzY1KiXo,1290
spacy/lang/es/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/es/__pycache__/examples.cpython-312.pyc,,
spacy/lang/es/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/es/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/es/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/es/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/es/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/es/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/es/examples.py,sha256=XfixVbJaV3wlp8yVztdKB5gfY2Ec4uu2VICu9XrJh8Y,778
spacy/lang/es/lemmatizer.py,sha256=gm4L75ypeK3Kbt82kk6A-AFSwqpC1wy5iPMsmbdqDdo,16022
spacy/lang/es/lex_attrs.py,sha256=CrDT968Ki9PNX2oASa8mOFqQE7o2gw_W75Yt7JthZAs,1795
spacy/lang/es/punctuation.py,sha256=q11X1Mu3l-tfpBsn5MEbEUSDHTU7x6pgOJFG6uMkxbg,1208
spacy/lang/es/stop_words.py,sha256=DFUab0NzL_P2KCyZjEYfeE4brnghjHA3sfXx23YwdH8,3388
spacy/lang/es/syntax_iterators.py,sha256=xjCGg5qUemfQ_f1EznIPGkqQ23Tur8bDk7HidJbBodk,2714
spacy/lang/es/tokenizer_exceptions.py,sha256=CnVAFKUfJrXM0NM4ZCheRYilzoBbzu-V1QSNJryJf_M,1457
spacy/lang/et/__init__.py,sha256=SBvBOxWxuNcnXuvKNJcx4TtQaHyeCm0E106p9k0Se8w,251
spacy/lang/et/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/et/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/et/stop_words.py,sha256=uyQWz1kqYbWlI2mGz8oQL_6bluNNdox77eJ9B2Y09uw,246
spacy/lang/eu/__init__.py,sha256=Kc1x49XsJ62zUPS-a_OKscrca-gPg4PbLdOsEH2a21M,387
spacy/lang/eu/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/eu/__pycache__/examples.cpython-312.pyc,,
spacy/lang/eu/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/eu/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/eu/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/eu/examples.py,sha256=39l1Q-SCn-urTfckHHuVj9EvR1jRtiW15-RPuF0QJxg,419
spacy/lang/eu/lex_attrs.py,sha256=oazLFM7S5WdkxIZRqwlY9OvV6BIe-iZtMmbOlZyekyM,1093
spacy/lang/eu/punctuation.py,sha256=Km9roqbUAJIFFYVKup9pMocqg-Rr0Rzr5FvZXHubyT4,77
spacy/lang/eu/stop_words.py,sha256=MHslu36-caVFbOqqSB8fnY4cY0uu64NKZLCHWIcC3PI,760
spacy/lang/fa/__init__.py,sha256=npXqoMki3Pk_9jhyv-oQzgMBoIE4JXaAgMAw33RQDHE,1307
spacy/lang/fa/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/fa/__pycache__/examples.cpython-312.pyc,,
spacy/lang/fa/__pycache__/generate_verbs_exc.cpython-312.pyc,,
spacy/lang/fa/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/fa/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/fa/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/fa/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/fa/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/fa/examples.py,sha256=YX8wFYxZSv2-db2hgEe-luZuX2B2JlFoIJ2HXGGZcWM,515
spacy/lang/fa/generate_verbs_exc.py,sha256=ziAiWuQw9iE43rdOZImBwCrepESgmEzuKPQHTxC8bJE,14856
spacy/lang/fa/lex_attrs.py,sha256=Ei-m8JYpBKpnB3kWXDSPjcO17EPxvBkVlczwEBp4-mc,1385
spacy/lang/fa/punctuation.py,sha256=-sxgi_DUMt8ReQuX6_swf0uNhmL_XtfgxaRfMYIoM30,508
spacy/lang/fa/stop_words.py,sha256=j-kJ9F_CgH9ZsGbZhTjb5exsUzHzX18m_KVVbqOsSbQ,3768
spacy/lang/fa/syntax_iterators.py,sha256=GLe-yUQPcmEztAM2JRfAI4MWXaYBGRPAlt7F0Mz4FpU,1556
spacy/lang/fa/tokenizer_exceptions.py,sha256=8_4DbEB5kYDlJFYQyCXtlEEt1-ne3khYEJbJKb_oAxM,64920
spacy/lang/fi/__init__.py,sha256=suEXBE5zcMAhhI_jJzxq-atQgZrOv6e3GoYDndabwwI,632
spacy/lang/fi/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/fi/__pycache__/examples.cpython-312.pyc,,
spacy/lang/fi/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/fi/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/fi/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/fi/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/fi/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/fi/examples.py,sha256=RVbQWY-lrslJL4Z_tE7kpRpY5XN-WfYdtjIEYW5MG9k,545
spacy/lang/fi/lex_attrs.py,sha256=9ldpJcWIxufs7S2T0XDFngEpilqwsbQ-oih7zRCLhe0,1076
spacy/lang/fi/punctuation.py,sha256=pRQ12_cqszPs8mHk2a8wbnQhV_ZfWVZe0WwpQriryrI,863
spacy/lang/fi/stop_words.py,sha256=YJONzH7fBCI-HskNpBrhYSCiOqUqiAwPlyjMq00AHqw,6436
spacy/lang/fi/syntax_iterators.py,sha256=nzSAQxlmuSIm9altVRU3HBYEu3OXZlyRX9PO8nuh94o,2380
spacy/lang/fi/tokenizer_exceptions.py,sha256=vO0uyLwnqzV3_WqIPWwkd2k-6GR6KyRSGd71qFBaMN4,2467
spacy/lang/fo/__init__.py,sha256=9SyKpmgVw2g9wgsBKCIWVny9CWXpw3yEta1bk36d7PI,471
spacy/lang/fo/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/fo/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/fo/tokenizer_exceptions.py,sha256=RuM-WbthExYmxbY9skNBtS7aDpG-nfWlZ4z_Bkzu7qk,1304
spacy/lang/fr/__init__.py,sha256=5VglEBNoe1dur_vfP2TK2iVLZWlNJGCGYEheYyjwNt4,1380
spacy/lang/fr/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/fr/__pycache__/_tokenizer_exceptions_list.cpython-312.pyc,,
spacy/lang/fr/__pycache__/examples.cpython-312.pyc,,
spacy/lang/fr/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/fr/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/fr/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/fr/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/fr/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/fr/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/fr/_tokenizer_exceptions_list.py,sha256=jzm0lG64EALayf_JFisuryFKikA7UInH9L5zuLGFmC0,360608
spacy/lang/fr/examples.py,sha256=bw2l8rJw32iJDRYzw2TR1Yov3VGbpzKx9yDRjZVylww,938
spacy/lang/fr/lemmatizer.py,sha256=yEUwoeZqPmDub_y3jxjBw2Hd-DtfIdp5I-iJmMnqyXs,3016
spacy/lang/fr/lex_attrs.py,sha256=WszflRMoke3l0pkwNIsQKwY65Pd3tqS125-3blrC7Xc,1626
spacy/lang/fr/punctuation.py,sha256=5DVtDArIrClgWWy9ysNFaQRnMMKwZVr6oPNtgZfKsmw,1468
spacy/lang/fr/stop_words.py,sha256=UVUdvzNC1D4IM9p2mzZf-hohFlqpjZ0JKbN6w1RidWs,3504
spacy/lang/fr/syntax_iterators.py,sha256=O5lamjjz_e25THH9D9Fen14zBruat7XYbyODcwym0aM,3124
spacy/lang/fr/tokenizer_exceptions.py,sha256=h2OzDpLu5Ol5RQJqV0S9BWfd63cxvGVrsR_J8EU3QlQ,11231
spacy/lang/ga/__init__.py,sha256=OE44PLNyA8OYf6mbOcsYw-CCf0q2yL9fgFKYmVkszpw,819
spacy/lang/ga/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ga/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/ga/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ga/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ga/lemmatizer.py,sha256=lClG39jiDtoSvpqVInKVSFkK1bK_DL5tlHGAxT3jl2g,4936
spacy/lang/ga/stop_words.py,sha256=qvhSUa0EHRkiHCM9WywpC-UWPb08ZHq9jf3hnM_z6xk,608
spacy/lang/ga/tokenizer_exceptions.py,sha256=RidFoAccsjqyKSmRfx-F2uHaaZgKFD6UTcxT5A_mzN8,1882
spacy/lang/gd/__init__.py,sha256=gvckYwtFXoFWXWqEwetu5ZYRQP4ud75Af2s_C3TdjtM,383
spacy/lang/gd/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/gd/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/gd/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/gd/stop_words.py,sha256=zXJFn3tiw6Nqt-AZUbtzS8LO7HJzYyKbRwSwML4g4m0,2368
spacy/lang/gd/tokenizer_exceptions.py,sha256=uQbMZAuesYGJEpX5z6I9fYNbT-k6mvJH7JAy5CGYTjE,24580
spacy/lang/grc/__init__.py,sha256=9iNNe1jt6bGGG1VMOPYZdo65Ukw_w9-FxNuD9SufwUg,620
spacy/lang/grc/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/grc/__pycache__/examples.cpython-312.pyc,,
spacy/lang/grc/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/grc/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/grc/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/grc/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/grc/examples.py,sha256=5DqkGm9O_COynYkv54yWXdIcC_3nJuFGXbtdH1HDNvk,1066
spacy/lang/grc/lex_attrs.py,sha256=6-ybQcIieb7EaVc9jmDI_KpbFwGFVqWBPVNX4HXW_40,6640
spacy/lang/grc/punctuation.py,sha256=EsotzNZZLTcODSWyGfkRcoDu-nO8zthZio776QGmKDI,1105
spacy/lang/grc/stop_words.py,sha256=6Q1dFsoRuZ1STrqRWDYwKfvxerTXu4Jy_1-jKM6bNb0,9364
spacy/lang/grc/tokenizer_exceptions.py,sha256=_TRIH-TGFlw1qFJRPn6AM_pYtuNTfvo7Qg3TOKrHaEk,6768
spacy/lang/gu/__init__.py,sha256=zu20XWUE4UHkr3WgP6XlqNqqnVhYijXHWP9Kt-0GEw8,251
spacy/lang/gu/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/gu/__pycache__/examples.cpython-312.pyc,,
spacy/lang/gu/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/gu/examples.py,sha256=YsWZgpsJvVCWIFPtEfsMCrmBh0REx1c7zcDCq8KuNQ0,1215
spacy/lang/gu/stop_words.py,sha256=gxoS28NtP_KGmwx09NhSY8CRtQ8cMKFvMaJMBP5Qp4c,1004
spacy/lang/he/__init__.py,sha256=OiAsDtgL8Djj6Cz7fDr3lBZF8SHTT4Rt8n2nCSamBsA,391
spacy/lang/he/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/he/__pycache__/examples.cpython-312.pyc,,
spacy/lang/he/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/he/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/he/examples.py,sha256=kRoLByay4Z-TlqfU2nv3VuiB5TYNzOwN4bGPnZm3qOc,994
spacy/lang/he/lex_attrs.py,sha256=lRBfnbDNMcvK6Ub3UidpL0JIWSVzAf00jgF4RPQlxj4,1759
spacy/lang/he/stop_words.py,sha256=vwSpeLz9S99TMPPdUyIdFMSTeOd581-t63iRHWaJPYY,1856
spacy/lang/hi/__init__.py,sha256=DuuZDrEbCJI39iWiryXwOD66D3H9ibmW7dUJUMdKTSs,305
spacy/lang/hi/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/hi/__pycache__/examples.cpython-312.pyc,,
spacy/lang/hi/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/hi/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/hi/examples.py,sha256=0wac1IHQvfgZrT5Vdt7B4nwa11mIW4hP2b34QpiX2DQ,1518
spacy/lang/hi/lex_attrs.py,sha256=KU20rA09zMLp3xVFQ5nEKuKxAxB_YEUAyFEeTNf1w7A,5888
spacy/lang/hi/stop_words.py,sha256=8On5px53EVeFuxMcJVBDy-0L5tA_MvF8_ByIe3xNHjY,2975
spacy/lang/hr/__init__.py,sha256=-IojPQAgL38rfTXCssxvKIk78Af1RY2T9edMKPinSRY,251
spacy/lang/hr/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/hr/__pycache__/examples.cpython-312.pyc,,
spacy/lang/hr/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/hr/examples.py,sha256=wLmuy50roWiJSseRql9-x0ElbYnvQS_nDtWyETsiqf4,489
spacy/lang/hr/lemma_lookup_license.txt,sha256=03KuMj0zjDbFYxslZxzWX65sEXFI7bXzvC9_FZVNpts,971
spacy/lang/hr/stop_words.py,sha256=bTxVezgH9sPwFTpAFmguyQ1i_KK6VBF_mNQbffrWs_Q,1999
spacy/lang/hsb/__init__.py,sha256=GZ-s5ctO_Koa--x3Xz4p0k5z7fBMnUCAQu8XISfEfE0,437
spacy/lang/hsb/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/hsb/__pycache__/examples.cpython-312.pyc,,
spacy/lang/hsb/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/hsb/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/hsb/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/hsb/examples.py,sha256=37Glysvvy_gdnhlpKuiNMc7678BNW1oMH6DS23Z1KiI,640
spacy/lang/hsb/lex_attrs.py,sha256=PLoPaE6nCMKEzDawrqm3E_cm-yKYdmJgZYKLMZoqH-s,1791
spacy/lang/hsb/stop_words.py,sha256=EBcFK5hGQ45cAXd1Oqk598trKmiNBnxUkGIjtL4A6wk,123
spacy/lang/hsb/tokenizer_exceptions.py,sha256=6Vhq9b-VGjBVthYCfYetUglrE0YYw1cgQO5x8f7lg9U,386
spacy/lang/hu/__init__.py,sha256=JjxG7-slcT6BSNHTPVC1Ybnr-4oM4DrG8fbmmAZih4A,584
spacy/lang/hu/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/hu/__pycache__/examples.cpython-312.pyc,,
spacy/lang/hu/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/hu/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/hu/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/hu/examples.py,sha256=9VOPy1uSkwCLLqVSEKQucqQGF7qiwnhO24dUGGPeM5c,407
spacy/lang/hu/punctuation.py,sha256=DJhF5AUb2NNQ_cvRTLpKkJyLVDUV9w3X05n1NH0rCoo,1505
spacy/lang/hu/stop_words.py,sha256=PGHIe97AMOz1OQ93JpQNWWnmHk3Rhp0abcL44UuIbFk,1384
spacy/lang/hu/tokenizer_exceptions.py,sha256=i10v3rvzytBkeWAsS_2fVlpqUKDsWNrmCp6Lbul1lak,8400
spacy/lang/hy/__init__.py,sha256=QPmWWgqTe9-RHxZuSZ61SdD1hW9PIMSQvjWtBmNZXSU,317
spacy/lang/hy/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/hy/__pycache__/examples.cpython-312.pyc,,
spacy/lang/hy/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/hy/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/hy/examples.py,sha256=p6WUN_4701CVDDCdwTz4B-ZcarE4878NOJft9rOHvFg,441
spacy/lang/hy/lex_attrs.py,sha256=ajoOxK3aW9y5fuoYIMcfwoZhy-2f-ZF7Cie_NSKf8fk,1159
spacy/lang/hy/stop_words.py,sha256=A7Eaeu5KIcq3Q12XWgIWurnMQxpm2PNMUujl5r0iti0,1067
spacy/lang/id/__init__.py,sha256=tXiZzjtdmoQ_M1t4GQJTAXdNYvRQddZ04_ejDV_k4Cs,698
spacy/lang/id/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/id/__pycache__/_tokenizer_exceptions_list.cpython-312.pyc,,
spacy/lang/id/__pycache__/examples.cpython-312.pyc,,
spacy/lang/id/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/id/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/id/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/id/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/id/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/id/_tokenizer_exceptions_list.py,sha256=Nyg3gDbW8gc-ny5Wiuj8oaltvf9DKhEWlXch96F9Zk0,53599
spacy/lang/id/examples.py,sha256=5YnQ-Si3g1WpYptoRzZ7J8sq0oy2K-guMbRSzQVToxk,726
spacy/lang/id/lex_attrs.py,sha256=z7VVgaBeooXiRsXztX9DaYsHr0pqDtRa2K1wKMU7rqU,1276
spacy/lang/id/punctuation.py,sha256=1fUinboJvB7FUthkKMAPwC4OuNJGVioJigj9Hxm03Yw,2138
spacy/lang/id/stop_words.py,sha256=_A6i4wW09h_Gg_NojqxZW9drF9RzpgYuwQCdf0kHbnk,6507
spacy/lang/id/syntax_iterators.py,sha256=B0p208fFQ-1S0E3WSiIBXu8hrC62uKFIQBYCt0fm6ns,1538
spacy/lang/id/tokenizer_exceptions.py,sha256=1CCpdeGZGIYVofwe73iNCNxBtdLuZSq5S34WF9tMODY,4204
spacy/lang/is/__init__.py,sha256=jWLOzkMS4Pc8fVyvfUuZ4-QF-B2Am7g6QJm3KxGTo48,255
spacy/lang/is/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/is/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/is/stop_words.py,sha256=T7ix99tepGAYg6Z7fUexoaeIIyovl7O1XuMDAh7sYoY,1019
spacy/lang/it/__init__.py,sha256=xhQN7h1VUh1nKCbumRmHHcprJSoWYn9QcK6YwoN5hoU,1230
spacy/lang/it/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/it/__pycache__/examples.cpython-312.pyc,,
spacy/lang/it/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/it/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/it/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/it/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/it/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/it/examples.py,sha256=OOXYZlP69krt5djnOKTlWoi_o7HpcBLOMXFqdpQUEu0,471
spacy/lang/it/lemmatizer.py,sha256=1aQgdScQ5YKwlaK4qWnkHpKX0Jj2zNUx0lpR7z9r-SM,4615
spacy/lang/it/punctuation.py,sha256=wYHSHxuj-jS5az7iPYVgq3-diSmPjuXd_Vcyu3Z8hgg,853
spacy/lang/it/stop_words.py,sha256=1XznTigPohkVAP6g6rrfqW4Jed0v2x4AcDKNWt_4_IE,4114
spacy/lang/it/syntax_iterators.py,sha256=XRNsG7j5QrXiW7VJG9nWZj3UIdxWKcBhsYsdP7k1-1M,3137
spacy/lang/it/tokenizer_exceptions.py,sha256=FrhPpFDBTVF1ljmtkZ9tmlu9m0EtUuk6SaIw1PTmx_s,1160
spacy/lang/ja/__init__.py,sha256=wND1owHnbdg87Dib2nXIWjK7j58Q3WvsdzbJ6veyV84,12572
spacy/lang/ja/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ja/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ja/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ja/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/ja/__pycache__/tag_bigram_map.cpython-312.pyc,,
spacy/lang/ja/__pycache__/tag_map.cpython-312.pyc,,
spacy/lang/ja/__pycache__/tag_orth_map.cpython-312.pyc,,
spacy/lang/ja/examples.py,sha256=WgPG3UeDIQFs1HMjfwUN4T3yhR-yCLcvj3na3-risgM,499
spacy/lang/ja/stop_words.py,sha256=X0UdyH77vesr4cur-BVYsayPpNoJlREzjxsIydUq4pg,1328
spacy/lang/ja/syntax_iterators.py,sha256=l3sytbu7xKK-sY_J1XcHwyPk4M_wDvLKg4QDp-kbTPU,1638
spacy/lang/ja/tag_bigram_map.py,sha256=OZmd5XQjurCyygR-9IunjBWERRDg3z7Jc45ttC6CVNY,1647
spacy/lang/ja/tag_map.py,sha256=xI7R3NMwRTJ_wlotplD3SmFsxzfnAKlc8b41ez4sE0g,3795
spacy/lang/ja/tag_orth_map.py,sha256=esatD6AYqxvwRTZYJq_EwKsC05k9kt_jckuB3fnP-8o,546
spacy/lang/kmr/__init__.py,sha256=8NFz0gdyMc1PNk7lMdjnUa1RGn9lAKeBLK8q6Q0Y5hE,318
spacy/lang/kmr/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/kmr/__pycache__/examples.cpython-312.pyc,,
spacy/lang/kmr/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/kmr/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/kmr/examples.py,sha256=d4uUoWnKCQdvnxYzA1dM_A7b7UAEL1b4xynNzIZqFr8,1326
spacy/lang/kmr/lex_attrs.py,sha256=V2ihnbv5myO7a0Xq9pbfaAkXgtqaPJXzRTWiXyVZeBY,2328
spacy/lang/kmr/stop_words.py,sha256=ovu7qd8ZBPeRP8vQ_rP3kUyYe5hsyNJPe9GP9IaTYek,203
spacy/lang/kn/__init__.py,sha256=uGXNho077Iwut2mcZ00TD-SgK7XsW63gfM9XkhaeeBk,247
spacy/lang/kn/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/kn/__pycache__/examples.cpython-312.pyc,,
spacy/lang/kn/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/kn/examples.py,sha256=nIhSJOf2ZecXywpK7ve33YDVNppkMeof0UgmYbG4Ba4,1211
spacy/lang/kn/stop_words.py,sha256=wntWkWVml_kPl1tVsQsarDflRDhxBlIME0A94ut1RK8,1253
spacy/lang/ko/__init__.py,sha256=T05M9Vjh4Dn_LYfmwW94DgkBIRnMk_4dbMhAsalkoCs,4251
spacy/lang/ko/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ko/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ko/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ko/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ko/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ko/__pycache__/tag_map.cpython-312.pyc,,
spacy/lang/ko/examples.py,sha256=O7IV4_6j--UrRRsf-k9c2FuYeU-Yse83NZQ8N4CaUzM,531
spacy/lang/ko/lex_attrs.py,sha256=rPN1Ml1KuVARNb07YEHOFYXjdr3qDuNJXWIf5G45FaE,1051
spacy/lang/ko/punctuation.py,sha256=iIbfCoVJiGtXZ4zYHI8vjMi6YSYDuRYx1-5psf5gMZw,267
spacy/lang/ko/stop_words.py,sha256=tntQHlQa00mP-g_zMJVN-I2vew7EVLBUaphK3eHA7VQ,349
spacy/lang/ko/tag_map.py,sha256=QXImSFOLId495YPT4We_i5wS-yaFeZwkClYX2-utnz0,1975
spacy/lang/ky/__init__.py,sha256=xfvq3XilbS0HhCifFOXaCtDG0VmVUU0kOaNnR2HplEs,487
spacy/lang/ky/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ky/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ky/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ky/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ky/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ky/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ky/examples.py,sha256=_HU0pWGMoIF43Ww1Zi-6w1uget7sDqoXGkRWzbsP6W4,917
spacy/lang/ky/lex_attrs.py,sha256=K0x6o7bQWUVYBQNdSl2EH-AlZ4yShrRgHJrhmdeBPT4,925
spacy/lang/ky/punctuation.py,sha256=eOrQiL-P_OH14wNFkkFQJv7eQeHEuOvBlyv38cB-k-4,855
spacy/lang/ky/stop_words.py,sha256=wb33t9miQOUap8h_QFshq0KIncp0xA4yut5D2qkTrYQ,1064
spacy/lang/ky/tokenizer_exceptions.py,sha256=bg5933WKLHqIT5TxuAo7901Qmsa0bnVUqbIqVGibckU,2049
spacy/lang/la/__init__.py,sha256=_4xauVWexWxHNR6mCneDPI_u_A0iAWtBadUds71C43E,495
spacy/lang/la/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/la/__pycache__/examples.cpython-312.pyc,,
spacy/lang/la/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/la/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/la/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/la/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/la/examples.py,sha256=hTFwJ-gsKkClYLvSB6Ml82HqFHI6B6QYvoicQk2HAi4,1146
spacy/lang/la/lex_attrs.py,sha256=t-xZV1NzOJ9S26UALoldJ8i_cAF7aZTl9DugnER19Ho,2372
spacy/lang/la/stop_words.py,sha256=OIsUS2aSskSmCnx09JW82dByV7n5v_LmT34LmdQEKFU,619
spacy/lang/la/syntax_iterators.py,sha256=mk71WY1z2AyVi7kpI2vYbpdWZa6bhKkVUQK7YjBn-Zk,2392
spacy/lang/la/tokenizer_exceptions.py,sha256=taPWnqZx0cIv0e8nDJfOEEOt5u1w30UNMKU_QPlOClE,1235
spacy/lang/lb/__init__.py,sha256=P57dkRm8uQLQBXCz713aE3y-Or8nd9nYPUYwOrZSp0k,515
spacy/lang/lb/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/lb/__pycache__/examples.cpython-312.pyc,,
spacy/lang/lb/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/lb/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/lb/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/lb/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/lb/examples.py,sha256=EAfrNunUSw69aZ4bazG9FXId4_MwCqksm8lFTCxRzlw,906
spacy/lang/lb/lex_attrs.py,sha256=Z-FiizsBAuklJ_fpFoGn-R4iOWnWxiv9C9OsoPrDh3E,1342
spacy/lang/lb/punctuation.py,sha256=WH8IVsR9BE0IElPSXg5J8KiEfchtIx5jp735A1ttVa0,641
spacy/lang/lb/stop_words.py,sha256=ZXIrAQ8n0horfNQz45LiWvSNqWl4pHD0k9vD2-DYgjw,1127
spacy/lang/lb/tokenizer_exceptions.py,sha256=LOoBt4n1RDmDEaqDne-W-4g2GHD84MbtRUzMBJP3HF8,1167
spacy/lang/lex_attrs.py,sha256=ogEo1VOodgR65BWc7FLWWMBMENyjuk9TIMQ1QeeRrFs,5943
spacy/lang/lg/__init__.py,sha256=IoHwKGw6oLRI9OxAaE5E22tCKLtnQP53rKNgXF0OrG0,388
spacy/lang/lg/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/lg/__pycache__/examples.cpython-312.pyc,,
spacy/lang/lg/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/lg/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/lg/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/lg/examples.py,sha256=0mLR1nVo1vX9NyuqqwRUUH59xWCam2FnNdWiyCol8Bg,522
spacy/lang/lg/lex_attrs.py,sha256=9G4dCIvh-bkPa_Nk7ZuNrKzPgJc1lAwhVRfUZLlgiyM,2680
spacy/lang/lg/punctuation.py,sha256=_SsU_xmi5_kDghJv40E4HLtUhr8jSItD_i9UI6E_KJM,576
spacy/lang/lg/stop_words.py,sha256=113wsYGkabS4OUid238f6_KhiJjYUSPrp67Qdk6Qyj4,1361
spacy/lang/lij/__init__.py,sha256=96seIR4bYncNXh9RHscR--0a_h0w3IRrz951KqOxmwY,430
spacy/lang/lij/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/lij/__pycache__/examples.cpython-312.pyc,,
spacy/lang/lij/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/lij/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/lij/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/lij/examples.py,sha256=8F-2s8y1VkAysV-sX3EFLHkYY4z_HlX4F7CcDb0jNY0,397
spacy/lang/lij/punctuation.py,sha256=63_wwNZRR6gwsife9ki3G1LFdLhR1hbyYosLVsFL2pU,269
spacy/lang/lij/stop_words.py,sha256=msCT14bjJb_DmvXRhFkkZf2NX7OUmhc0mLdfwtk12sE,853
spacy/lang/lij/tokenizer_exceptions.py,sha256=6d9ymtI7IyCOYALs_psV251YwAiW7uqbHDYgPs1lVDU,870
spacy/lang/lt/__init__.py,sha256=NyZfIavEbupGg-ETTvpFyHx7fRDsdRMVQoDmsbkN89Y,557
spacy/lang/lt/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/lt/__pycache__/examples.cpython-312.pyc,,
spacy/lang/lt/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/lt/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/lt/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/lt/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/lt/examples.py,sha256=9wbF6gV50-AG38rdxLLSUoQz4ZlIlZjGYXac67_89rM,602
spacy/lang/lt/lex_attrs.py,sha256=e7Pnmw7Xb33YDRL44qU1NOIrYnYQhnLmm_43bWpPeS4,22464
spacy/lang/lt/punctuation.py,sha256=DdBgGxp3v-0nZ7UP7ihbbYfhdmw_wkwLcxTopExvwUI,696
spacy/lang/lt/stop_words.py,sha256=MN5S7EIMb0M-9u7xy9aJFX8tl5gGtbSutAbXcmN7aog,19708
spacy/lang/lt/tokenizer_exceptions.py,sha256=jspmUr4J6oimib03d76_SSu57MxELtuzgzw7jWRuXCA,382
spacy/lang/lv/__init__.py,sha256=hflmGplU8LT3l_RNCzc9v3xDw4dTimNaUek8iTEwiQY,247
spacy/lang/lv/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/lv/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/lv/stop_words.py,sha256=7UPJeAIjTFIAwK-IoTq3UN5NMlk0fCmOjMPqbAVKGTI,1085
spacy/lang/mk/__init__.py,sha256=zTYGZcsVyIgsqK4bDobG7kqGfasGHm9LI_2psurmrd0,1511
spacy/lang/mk/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/mk/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/mk/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/mk/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/mk/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/mk/lemmatizer.py,sha256=tzOpbqCkCjnHmANTP4j_sU1Oeq0Wob_m6fVvM82MQg4,1718
spacy/lang/mk/lex_attrs.py,sha256=Tt7WsfhJhlVbNH4fCuGAO193lHjB5xFHVChENl8P6QE,3468
spacy/lang/mk/stop_words.py,sha256=DPecT5Ydzd1eW9wxIyaFn7Er13hsppXfuP0YZa2XaX4,9106
spacy/lang/mk/tokenizer_exceptions.py,sha256=_y4WSyP8q5iCY0UpQ29TyRIm3BpjnG07gVoHFOm-R-Y,3576
spacy/lang/ml/__init__.py,sha256=ZNMeRAOTI5yl9Qpju2MvduG9HDppS9UHh81z_gTM9Xo,321
spacy/lang/ml/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ml/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ml/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ml/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ml/examples.py,sha256=X98k06MthvTBUqkWiur3qkaSWcVX1EEKGJDZjvi5pQs,1403
spacy/lang/ml/lex_attrs.py,sha256=ZN5U87YjI11I2Sngep6UluwJa6Zp-hCBtlgmnqhYM-E,2344
spacy/lang/ml/stop_words.py,sha256=N1dea0kKbh6Nk5p3rgjJe4osSeHzEV4-k7aX6Ry2RKY,184
spacy/lang/mr/__init__.py,sha256=xSX-WiaBNLby_WKv8Fm3zI6njhoLa037nCVZF1LovjM,247
spacy/lang/mr/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/mr/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/mr/stop_words.py,sha256=ltOV_oQlg8OgmI1-hAPnqdmGYIEp6WkUJHiEj3wLrFk,2456
spacy/lang/ms/__init__.py,sha256=UsVBUtN3V4YC01eQ5XlGUauFlSt6XuDd8OtSU8JXiB4,678
spacy/lang/ms/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ms/__pycache__/_tokenizer_exceptions_list.cpython-312.pyc,,
spacy/lang/ms/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ms/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ms/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ms/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ms/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/ms/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ms/_tokenizer_exceptions_list.py,sha256=ZW9cj8Orc1HC9OAODRah2XDn2uooTtKMVTZMz0re890,27445
spacy/lang/ms/examples.py,sha256=WUW3evk3FhkuW8MOObc1UzOHhCP0ahzsju8wwY-Ixg0,680
spacy/lang/ms/lex_attrs.py,sha256=CS4FjzCvRNthr-AqOPnHDdFe-naQ7ctwuQAg5pWRbQY,1265
spacy/lang/ms/punctuation.py,sha256=3fs634jPavhn0Ys6a8MuzsvgG17SPwnIkV10pnBw1Dk,2134
spacy/lang/ms/stop_words.py,sha256=_A6i4wW09h_Gg_NojqxZW9drF9RzpgYuwQCdf0kHbnk,6507
spacy/lang/ms/syntax_iterators.py,sha256=B0p208fFQ-1S0E3WSiIBXu8hrC62uKFIQBYCt0fm6ns,1538
spacy/lang/ms/tokenizer_exceptions.py,sha256=dqAAILrMNZNUhHJ7SCERf6U1WyzbXhzqRFQv8uiTeDw,19109
spacy/lang/nb/__init__.py,sha256=nH-z_GbAV6mwtZ8uu043EKgTEItAFQ_woGAIPO2a_lM,1274
spacy/lang/nb/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/nb/__pycache__/examples.cpython-312.pyc,,
spacy/lang/nb/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/nb/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/nb/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/nb/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/nb/examples.py,sha256=5DNoVj5AQ5lqeI-XEyh2FU_zz-9y6l2DRnqdkzhYYjE,428
spacy/lang/nb/punctuation.py,sha256=U5bxfVbJQ8IuDIrKG47ZP6dRWMxo63UM4c9dlJKfRiY,1663
spacy/lang/nb/stop_words.py,sha256=bFy81Dye49y_Gipz3611Vw8NHcXd53po5PYitE9oEzM,1160
spacy/lang/nb/syntax_iterators.py,sha256=jP8_aAZYYDNBvjx6GA_c3QVU8cNfp5dGXYdFcjjenn4,1523
spacy/lang/nb/tokenizer_exceptions.py,sha256=zfH6wY70DsiEhAiQNrNmQG3BPagBWmNZ8pWXxZZbeOA,3068
spacy/lang/ne/__init__.py,sha256=46MZsMoRn3ryXkO5HNlg3pPwaOOGkQZtMmNHpDZJOB8,309
spacy/lang/ne/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ne/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ne/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ne/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ne/examples.py,sha256=afi2Y9s72v1b5dtU1Tmt1OYUZ5M999ml1hxDGw72gkM,1104
spacy/lang/ne/lex_attrs.py,sha256=eCn87ifzYyQZd2Fn_JuBQnnaOdVNwNIX2KGfu0gHhF0,3275
spacy/lang/ne/stop_words.py,sha256=U4fW3CzMG4HnBSEMQccMGppxvlnLWxlCvWU_NGVILM0,7135
spacy/lang/nl/__init__.py,sha256=M7XSZS6jVytcSg3E8qFwQVsa-RSwPTI1q9Zx5y5OOtM,1330
spacy/lang/nl/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/nl/__pycache__/examples.cpython-312.pyc,,
spacy/lang/nl/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/nl/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/nl/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/nl/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/nl/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/nl/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/nl/examples.py,sha256=t2vFBC0xm67X0uIfkdsKVStSdOX2Zbs1SZmEJWCrCtY,441
spacy/lang/nl/lemmatizer.py,sha256=KruRMHfKlegAfRALfLk7bZo5vCjoVNer3J8oBFVjlTM,4618
spacy/lang/nl/lex_attrs.py,sha256=vRgAReyjMi77CFqaAsnyGUgR1wSBTxFCM5ggNoH92aQ,1302
spacy/lang/nl/punctuation.py,sha256=OdHPUOhV2DjVX6u59Xmufc3v6-3bxn7e7GUv0Sw5BgE,1538
spacy/lang/nl/stop_words.py,sha256=-JYR130qlJ1N9i5uAvAX4JYNxthPQwjxLf1GSX57xqI,3090
spacy/lang/nl/syntax_iterators.py,sha256=qO2f08pxt6lywH_CF-133-6g0G2meevzzpMEPfeM7GA,2868
spacy/lang/nl/tokenizer_exceptions.py,sha256=ReUgOjlPrXIek0vMjh8bMLwHE86NpCVl72uEsrbLr-Y,24298
spacy/lang/nn/__init__.py,sha256=ObkXA8C5MeE_tPe3GlzjYssSq38yUrsb5GcCzV657Iw,580
spacy/lang/nn/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/nn/__pycache__/examples.cpython-312.pyc,,
spacy/lang/nn/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/nn/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/nn/examples.py,sha256=LDKb-KmxA2WGAAqQZweIgSJ0NgiUlm2QOpFTnzFScZU,675
spacy/lang/nn/punctuation.py,sha256=y2L8KQ-r0SvdDMP4jubXbnZmkZ86yqlAujNNzJdEKwk,1796
spacy/lang/nn/tokenizer_exceptions.py,sha256=ni4JKoFrhpXdgAD8ann8tigC6eMbVC7I4U9dwrAlWeI,3298
spacy/lang/norm_exceptions.py,sha256=QXbyredk3cH_Yfan5HdOcG7YKVfcvMuMk2_4K8knTbc,1425
spacy/lang/pl/__init__.py,sha256=jEI-G_srUy1YkK0JufQnyUO4oS9tX__KUSOi7JT1SMU,1358
spacy/lang/pl/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/pl/__pycache__/examples.cpython-312.pyc,,
spacy/lang/pl/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/pl/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/pl/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/pl/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/pl/examples.py,sha256=iBulBIsxtoI398-zTGBHcSyUxFRXdoJrH2R7olwSC8A,685
spacy/lang/pl/lemmatizer.py,sha256=gzmCkYh69mVuab9LnJb5FKDXpFA45iVSo2OI-rmG7eM,3566
spacy/lang/pl/lex_attrs.py,sha256=V6389hpC_pmCBXRUkaWv6SL7dmCJUK6x-mLrQAVlEu4,1192
spacy/lang/pl/punctuation.py,sha256=-i_AA8myqh9eR3z5R3eX_yghrM8_buv_yrCriN8aD-E,1363
spacy/lang/pl/stop_words.py,sha256=Om3dygebXETH3MLiAgOWIMWRxvMTGei4NmPhVbK_MRQ,2360
spacy/lang/pt/__init__.py,sha256=qnUZCLSHroOMIhXCOshzcZiaCvFp__wFulR-yRuXGNw,644
spacy/lang/pt/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/pt/__pycache__/examples.cpython-312.pyc,,
spacy/lang/pt/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/pt/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/pt/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/pt/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/pt/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/pt/examples.py,sha256=PNPvhX3dz6ZyaQKdZ6oKNqOQFqZtZY4TLKdUBzQ8oLA,472
spacy/lang/pt/lex_attrs.py,sha256=AJLiR50oKI7ArmQxx47TELfz_yZQ7QifoHPhBYnSkmg,2037
spacy/lang/pt/punctuation.py,sha256=Xf9aZa-FNleJXjeATc8ay8A6vOaO0dhZjzRnJnAV7TI,456
spacy/lang/pt/stop_words.py,sha256=obwfwsF-T7AWhV_v3QROBFIbG12-mNFodggtEWIrpMw,2566
spacy/lang/pt/syntax_iterators.py,sha256=fG-oVp3hQZ4wrxo9mynfzi51qKM4D8HKQYXrLI9jjao,3088
spacy/lang/pt/tokenizer_exceptions.py,sha256=wmdsOvxx4_2Gq8OmU8vic-iSnlZPn3Lvz-0nybQWzZE,715
spacy/lang/punctuation.py,sha256=de0R4gO6SjDqh3YLiYcxGI2UR76Bzz3sioes-Sh16VI,2202
spacy/lang/ro/__init__.py,sha256=S67CTiVluMSI_g0Slb69WNoiwF1jICLhH1lFI1OE65s,778
spacy/lang/ro/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ro/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ro/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ro/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ro/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ro/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ro/examples.py,sha256=0DzqMisbpbJ0tuFTq0CAP3tr3x0YSJ6DU4aL1sqE1no,601
spacy/lang/ro/lex_attrs.py,sha256=8RTIhbE0CP-B2fx92NkwyC5bHorui_glSvZxvUIfH-0,1679
spacy/lang/ro/punctuation.py,sha256=wdKJ79BId3uy1uz27dPMoJ0TDZ98iSg8AyDu0Nar4rI,3154
spacy/lang/ro/stop_words.py,sha256=Zc_qUQdpjhg8XLwxew5m7LwIoo8N1qLM_6SrwmcJyr4,2945
spacy/lang/ro/tokenizer_exceptions.py,sha256=v_hLu-2h2rALtucCF4XZXD5PA7U0S9XvuP5A11x_NrI,1460
spacy/lang/ru/__init__.py,sha256=HoaeDjirzoXILJLfJSYuvj9wuzB6pdmaQ6CAGln4_IA,1306
spacy/lang/ru/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ru/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ru/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/ru/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ru/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ru/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ru/examples.py,sha256=gNvBatvrIHFT4xFW_nV0AyMXr4ULQ-a66AkuzIDC1sY,4581
spacy/lang/ru/lemmatizer.py,sha256=DqdWglfLzcygbWACOtedUuN4bN2eOQceNB665T9CwB8,7975
spacy/lang/ru/lex_attrs.py,sha256=8fwB3SXAL-Vpd3W5D4PPJ6MwZI6daDsI-QI7YZ34fCw,18733
spacy/lang/ru/stop_words.py,sha256=TOdw-9EfVZ4LH34fadPBI_UX7ZpKkrJJIkOHgYYUBLU,8356
spacy/lang/ru/tokenizer_exceptions.py,sha256=8r44j33eh4K0-7SigKQAh1EyxtmkjtnXpguh4FVuYjI,25394
spacy/lang/sa/__init__.py,sha256=vao2xLb5kPvGV5Q8d6YjDynfzZiD2RzgxIejUYx14Ik,317
spacy/lang/sa/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/sa/__pycache__/examples.cpython-312.pyc,,
spacy/lang/sa/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/sa/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/sa/examples.py,sha256=ObyP9_higAoiX8jhYnKBuLkFJl8GxmvGCoJzCajbpE8,781
spacy/lang/sa/lex_attrs.py,sha256=o3BqQ-cjV5Vm779_Qsu5iDx5Iif-0biS_Q-Bh0nFssI,4211
spacy/lang/sa/stop_words.py,sha256=3ijJ9oNqtDmvBMUTooK87ibItpxGTBw5sM5WJGDBa2E,9364
spacy/lang/si/__init__.py,sha256=dwS_AvDrdi1k19h_BIKN5gGRWJWaQScjjRGkIsXDscs,313
spacy/lang/si/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/si/__pycache__/examples.cpython-312.pyc,,
spacy/lang/si/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/si/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/si/examples.py,sha256=F6ebqjz7Ydv_q6Gj6-um9vMEJBaiKE_rh_xiwtOlL4E,944
spacy/lang/si/lex_attrs.py,sha256=9ns-RwUO57YBaPjZ2KAp1LgSWvgIrniqqAHhmCsm2tY,1253
spacy/lang/si/stop_words.py,sha256=wJdsnqdgYA5zNjY2Zs8EuJhs_tIpK9XZLTHIL8jXuHY,2407
spacy/lang/sk/__init__.py,sha256=XTFWVceeg4Q0jubLM21gunCshze_1eSaNSx2tNDZfW8,309
spacy/lang/sk/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/sk/__pycache__/examples.cpython-312.pyc,,
spacy/lang/sk/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/sk/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/sk/examples.py,sha256=N1FmARxm1FVS1joMr7HECVRwphp7eqjc95DlRCfKwK0,729
spacy/lang/sk/lex_attrs.py,sha256=E2wToIwB6dVndQrXivK5dd621z3voB7n4dAbWKYxQT0,1070
spacy/lang/sk/stop_words.py,sha256=QWvkT0nbBIAY4b7lZ-y12qXBF5rgFBm9NEZ-ZytCCC8,2639
spacy/lang/sl/__init__.py,sha256=pwl8mRGqIiq9pxxnq-kVyazNJrlI2XDwrKhPTXkDmYs,607
spacy/lang/sl/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/sl/__pycache__/examples.cpython-312.pyc,,
spacy/lang/sl/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/sl/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/sl/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/sl/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/sl/examples.py,sha256=8DAEe6VWpi5KTRG3XZXboTTj45O_wTOb8JcBVyso3Hk,575
spacy/lang/sl/lex_attrs.py,sha256=AwduqfN_m1jPIxztvy-Cn5ABwnlua6ek275l7AbsK0M,6583
spacy/lang/sl/punctuation.py,sha256=wOA9KNlB6BF7K2_b6yPhPPl9srl2lGw28Zw9x2g_tE4,3222
spacy/lang/sl/stop_words.py,sha256=kquMKgz9FBSbK7FJxKDTd8HEdV0Ol6jTWz2a-gBKztw,2478
spacy/lang/sl/tokenizer_exceptions.py,sha256=II3ZiRT0GccrobBBP1LDxXc9PWHZmF_GErIFJPc5a6E,13429
spacy/lang/sq/__init__.py,sha256=1KaslNqL5pgfkrdqkO8UpISgq3Ffdo6hdTFYrjFBPXE,251
spacy/lang/sq/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/sq/__pycache__/examples.cpython-312.pyc,,
spacy/lang/sq/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/sq/examples.py,sha256=YXEZSHWqX1NzpsPr7byJElR8b7lR-2tfoS3fnYNzrQM,467
spacy/lang/sq/stop_words.py,sha256=clzMINrrJQTgxGYzJ35eWCcTBqd8Y42PhahVzLTgSuM,1210
spacy/lang/sr/__init__.py,sha256=jtEOQBiRJOt6wtDdClaOfA8n7GtpAuR8Dk9NzuFfEjs,545
spacy/lang/sr/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/sr/__pycache__/examples.cpython-312.pyc,,
spacy/lang/sr/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/sr/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/sr/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/sr/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/sr/examples.py,sha256=pFZCbXtXOhc-4Dua4ie9TFpWa5JKimhH8SB0uLSMVbQ,910
spacy/lang/sr/lemma_lookup_licence.txt,sha256=EhBufMIJ86nhfs8uEAKQ2maXAbNJrR_bXUrEcvPjqO8,1549
spacy/lang/sr/lex_attrs.py,sha256=gutD0rOlGw4F8yT706vLthgU6dgMzYreAa7sredHxKA,1425
spacy/lang/sr/punctuation.py,sha256=fSm8Gm0hbzjvPsZxKz5-FeVxN2ofK5iXuP61xq7TXWY,960
spacy/lang/sr/stop_words.py,sha256=AxPa_GHfxsfKW7uQcemws9l9716sVXgMbLSpB8EMwQA,3895
spacy/lang/sr/tokenizer_exceptions.py,sha256=IgmMF0jlUWXthajYtp8Z2j5rokZEhRNcwPlUgDUVy0Q,3523
spacy/lang/sv/__init__.py,sha256=QoDQLZvD1a4ZfpATyaYgd69be1c7Kl6hYS66vNsmpXo,1276
spacy/lang/sv/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/sv/__pycache__/examples.cpython-312.pyc,,
spacy/lang/sv/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/sv/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/sv/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/sv/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/sv/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/sv/examples.py,sha256=ovmfgGwVkp5j1BeqFvJ-0TshSoG5XKC2mNuLnQ3e2VM,441
spacy/lang/sv/lex_attrs.py,sha256=KPftyWF88EKcyt8u5Bd8021tt39P7DhHd8RpMQO319w,953
spacy/lang/sv/punctuation.py,sha256=DNVq5gNe3NtxNMd-Rm7UtVVzsXEB46F1ygl6vuBdWSg,1025
spacy/lang/sv/stop_words.py,sha256=BxGJ2zx5EoKZEtRuXhAkbTlfS4X4ML1tFmCgzSIQ99k,2545
spacy/lang/sv/syntax_iterators.py,sha256=d9jthD_dre5ARZwmjQrBGelYFANLBWf3D2TNPIJvMoA,1531
spacy/lang/sv/tokenizer_exceptions.py,sha256=l6dCnG0gmaZycrdNQwFqG_zhKPxBUOQwL00xyOGB3-M,3655
spacy/lang/ta/__init__.py,sha256=vyClTs87DZZAiaGlMMA_oYrzy0RqWlbSOPMxEzboqPg,305
spacy/lang/ta/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ta/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ta/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ta/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ta/examples.py,sha256=WCfjFG6_T-v7bNuJaqPip2rQYoezm5nI5YAY1TedC9U,2703
spacy/lang/ta/lex_attrs.py,sha256=-Pd1BObVb9RgZH0-4GbbsainaCrNfvDoG0pkLQ4XIZ4,2287
spacy/lang/ta/stop_words.py,sha256=d97PhQ9E3SH9PiVF_-OUpYLkIKpjeON89zYx5M2b36k,2018
spacy/lang/te/__init__.py,sha256=oZj5RWarWG-7JZA4Sq2zepIeyihQ3_73hCB9A4SyCLM,309
spacy/lang/te/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/te/__pycache__/examples.cpython-312.pyc,,
spacy/lang/te/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/te/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/te/examples.py,sha256=s0GdHbiI819E245hEl2wI8ZUwvJqYZ_GAMBN70s5FXQ,1243
spacy/lang/te/lex_attrs.py,sha256=RHggESZfpHDohz55ypCjmIR0vuuxkan0EN65cBlBax4,1263
spacy/lang/te/stop_words.py,sha256=51WkXwQVp5H5NUW5TfSs5XVsR14MRMsyS7VRQnsifmw,1107
spacy/lang/th/__init__.py,sha256=aVlRNDIUh45joBr6RMWJiMwFwNG-X_hGAKIT141mDYo,1324
spacy/lang/th/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/th/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/th/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/th/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/th/lex_attrs.py,sha256=GkkWE8Ar8ZLaYNbU-P6rl1FXd0N1m-SoQbFt358zyZA,1477
spacy/lang/th/stop_words.py,sha256=UK8Hjjl4uFHwPT2-58e9WnopDKZgMRZuAdWeVD1CnTA,19463
spacy/lang/th/tokenizer_exceptions.py,sha256=XgtZCV4Lygdok9yzZKMUu-Xm0UteM9tUnYDmpH2MhLE,18333
spacy/lang/ti/__init__.py,sha256=7gS8jzWhwQrF7X7j0pJ4d7K4PgxZ4ObFMKeCRSUcYwY,834
spacy/lang/ti/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ti/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ti/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ti/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ti/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ti/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ti/examples.py,sha256=FzVB8VCli6UkDh4kI9DHPPL9BySZfCUC1VQShEHmqeI,821
spacy/lang/ti/lex_attrs.py,sha256=MAhU_CP7q2TIQ9ClL4Qq-f9EJ7m9pYqQnba19t1LUL4,1508
spacy/lang/ti/punctuation.py,sha256=Rl0fl79o2Tv5j33KFNgomjse3YioTCV9PnqNJUCuM3s,548
spacy/lang/ti/stop_words.py,sha256=OxcgaW4bNpBSP-tcN8-49SFI4hFVxyWZdlxuCGDMM8E,1977
spacy/lang/ti/tokenizer_exceptions.py,sha256=edIGHpZ8zrqP1-ibylrqhiVm_AU8iy4vfwnqbtrpLPg,338
spacy/lang/tl/__init__.py,sha256=WvUfcF1avgDXxgJsaUPua342IyLhjjYr-MO4z3NfMmM,416
spacy/lang/tl/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/tl/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/tl/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/tl/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/tl/lex_attrs.py,sha256=TbXJdDruiAc9MtJImx_nhsu1fB3fqEYBDc49LAl3Bp0,942
spacy/lang/tl/stop_words.py,sha256=DQB4q1WG-nb9M5vtt9zWybXCsXOHpaSnWsFnmmInWuw,965
spacy/lang/tl/tokenizer_exceptions.py,sha256=opb55AptRF3Rc4Y5QBBhAmtUeGwxoJI6_C--oloeeas,687
spacy/lang/tn/__init__.py,sha256=NCoEn7t5e2L1hf2313wECBOHMrs4RvIjhkLHRdinuhY,392
spacy/lang/tn/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/tn/__pycache__/examples.cpython-312.pyc,,
spacy/lang/tn/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/tn/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/tn/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/tn/examples.py,sha256=NIZ7ErTpo6p-qQCXajonm0FOzySWTZjUCMszzRvgkfU,436
spacy/lang/tn/lex_attrs.py,sha256=ZJL_Z6SYUDGtNOLa1qGMdPO0bNRhkvZZGgnkFb9_KCA,2050
spacy/lang/tn/punctuation.py,sha256=NE246fA9_BmJyArRIROb48Z02x03wk2m1gmuHYbqlz0,602
spacy/lang/tn/stop_words.py,sha256=WEOgc_FnSf52V-mdMooCOPyjahEzvD1zHYZHUMytBmM,816
spacy/lang/tokenizer_exceptions.py,sha256=fb2WAZ9E7UDNF8yED_JQLBjWiOwCTIpkYIa7ShxS1do,3475
spacy/lang/tr/__init__.py,sha256=0v2beFivPpFkXpyob5HTmUrgsrxhQiF11OOrUtE7om8,546
spacy/lang/tr/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/tr/__pycache__/examples.cpython-312.pyc,,
spacy/lang/tr/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/tr/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/tr/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/tr/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/tr/examples.py,sha256=uVv2FRvfbrctvAckr_uuzGQxnDU2juU33n0izs-FD-w,727
spacy/lang/tr/lex_attrs.py,sha256=UXPV1ua-mU2w26VMAQKAc6lQ89OKHfk8tKR_tMsE_es,1673
spacy/lang/tr/stop_words.py,sha256=eXf6H7Bfryv9RWI_GsSTiFSIysqdijRcv4xAIxIfUks,4506
spacy/lang/tr/syntax_iterators.py,sha256=avxm9dMM_I9aFlqGJn3Vdmfb_tZvh4fEbrRgns1gdvs,1854
spacy/lang/tr/tokenizer_exceptions.py,sha256=5N_mqM08Cy1EWzJY3LDfq-joohC7vAAn1ZcL-M8P09I,6091
spacy/lang/tt/__init__.py,sha256=U0_6coRwSKZHedS1VElfu3hWhwaVxBbsKJe6LedUuZA,483
spacy/lang/tt/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/tt/__pycache__/examples.cpython-312.pyc,,
spacy/lang/tt/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/tt/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/tt/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/tt/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/tt/examples.py,sha256=Ev80w65lYgIqUWyuJ8yD_2y2gawVi1qQwgRuE5yGceA,897
spacy/lang/tt/lex_attrs.py,sha256=_3uEVHtqdSuyD5qnuNExjIe99NR6N2IzemKzLrF9Wsc,1117
spacy/lang/tt/punctuation.py,sha256=vSK2d3JNi2-iCF-Rcbzd945dMq-POG_-hdQEivzbKjQ,806
spacy/lang/tt/stop_words.py,sha256=SDVsFquP9Q9RhVhlaAXh4WxjwcxR1KPYmxcoGoLgaBg,18567
spacy/lang/tt/tokenizer_exceptions.py,sha256=fh6Y6VH6jNzUgLk3BM38atQRz8epQZqnb5eQYOx1zAI,1760
spacy/lang/uk/__init__.py,sha256=rqkhLh2_y2dsvauKXDZo3aBkS0XuuimDVsS4WulzVZw,1320
spacy/lang/uk/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/uk/__pycache__/examples.cpython-312.pyc,,
spacy/lang/uk/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/uk/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/uk/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/uk/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/uk/examples.py,sha256=CsIboqz8r0cwb-3ghjWivm2lzBqyjWDBbK3_joUt-YE,1798
spacy/lang/uk/lemmatizer.py,sha256=PB-Meu-qQK37RaOBK4Wq11v9vCLpqEk1eAALOEgnLgQ,1716
spacy/lang/uk/lex_attrs.py,sha256=XeCXGlSWR2mx09gmpPKfGf8IrSOvygYlQldW5Axka4s,1575
spacy/lang/uk/stop_words.py,sha256=yiAT3vZz00YOwj6OSIX9J2GPJBcAtqhrHhdgrGDkY0I,4887
spacy/lang/uk/tokenizer_exceptions.py,sha256=KAM2m2LnYYEm2h3csO4-SfHfqMdrAjIMJh2ZBC4CITg,1388
spacy/lang/ur/__init__.py,sha256=0rEwvOIVhz0z71lQ1Jj8oqNU2h6na37kRfS0AfXht3w,461
spacy/lang/ur/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ur/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ur/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ur/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ur/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ur/examples.py,sha256=f2WMldu6od71lpBxuKwNp3xEcPy_5BB9P9YrwEEeWu4,303
spacy/lang/ur/lex_attrs.py,sha256=y3gTnMX12uIWYJqR-fkVphPwsl4ldn1ddjUvVvMyZYE,2237
spacy/lang/ur/punctuation.py,sha256=Km9roqbUAJIFFYVKup9pMocqg-Rr0Rzr5FvZXHubyT4,77
spacy/lang/ur/stop_words.py,sha256=OpofjVT-2F2YxiBsEokXMg-OMIOySGRK4F2MSRTeZ_E,4722
spacy/lang/vi/__init__.py,sha256=LJmPthDmrPsm-HkRpW8mQ_vhiPS1BHHdcfy3F4NfNQ8,5522
spacy/lang/vi/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/vi/__pycache__/examples.cpython-312.pyc,,
spacy/lang/vi/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/vi/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/vi/examples.py,sha256=YthpR2IDxgimg6WUvsdCWZAV0K0b78XE5PDBQbGBKA4,769
spacy/lang/vi/lex_attrs.py,sha256=comL2rb6_H8GorlF4aMKsYs_ihESp9ArpI1VrL5_EnU,1396
spacy/lang/vi/stop_words.py,sha256=wdK6VZM1cNrBqpiC74CnEZbnzB186MKlUGWnSVcFA60,20595
spacy/lang/xx/__init__.py,sha256=OSHg0rqJ60BZf4Z_wARSymKTIJwzl_0pVPinm5ZflSw,266
spacy/lang/xx/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/xx/__pycache__/examples.cpython-312.pyc,,
spacy/lang/xx/examples.py,sha256=PaLbIU0s7zksEeJwQyWQgEGpZPhcOhX1fUUTyWbEPCs,8161
spacy/lang/yo/__init__.py,sha256=jTMxH48qBCyIkBFqu8vY-yjviXIRn2seCEgU2OMgkSw,309
spacy/lang/yo/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/yo/__pycache__/examples.cpython-312.pyc,,
spacy/lang/yo/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/yo/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/yo/examples.py,sha256=MIf1Vr3yOp4j49pUosukEcKUYtHrk7ZhYV5K44urPyY,1269
spacy/lang/yo/lex_attrs.py,sha256=k6Mq0XxcWjuB72MJXUqPaExV45-wYB-cZiwu50oNj4g,2522
spacy/lang/yo/stop_words.py,sha256=0zn0LQ3mGv-Hc_v3pNGaib8lxxIuqt8V_SnKh31TQ4Q,608
spacy/lang/zh/__init__.py,sha256=maS8tbQrNF2SN4HpPNjQuiM9b7MUHIgLha00fk6xhJ0,12688
spacy/lang/zh/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/zh/__pycache__/examples.cpython-312.pyc,,
spacy/lang/zh/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/zh/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/zh/examples.py,sha256=jhcV7zB2u4YysJTqy2WqVrMLRwtf8H6GoygKRFu8f0k,792
spacy/lang/zh/lex_attrs.py,sha256=RnMA0UTkn8pTD0GyvtU4dAjy9N6Gqpj9-i1kA7TZwmQ,1612
spacy/lang/zh/stop_words.py,sha256=uNxPGdpTvWiC81IP9VuwUFIO3mXOwST86vpUSA-betc,13409
spacy/language.py,sha256=lZ5epcFZRmxzudq7cYa7GivlTMEdd9hGB-rBKaQUOS0,106750
spacy/lexeme.cpython-312-darwin.so,sha256=EakekQAoT1CYt_eQyUGPaGgJDjlbUOCEJaufHsSylq8,160672
spacy/lexeme.pxd,sha256=yyy9ULgXmtdSbhfqbV2WTCf3iBcnCWhHLwAiy_szO1g,2682
spacy/lexeme.pyi,sha256=wdnL3DF6txv3mHkRucrJepEYfRKwCcTDNxQ2E-GrWyo,1435
spacy/lexeme.pyx,sha256=CF9Hfl5Cr311vNcbb3UAtXaCIrhe_msI8kbtmcTHhJ4,16656
spacy/lookups.py,sha256=1vSxgGNC8DKbJ5nUEiHhBmxCRJMcys13L1Et5nILCIc,10851
spacy/matcher/__init__.py,sha256=e7pqTJyfaB4JbqMYOIpq2yieKF4cV6VMIZGBYLa1Pgw,232
spacy/matcher/__pycache__/__init__.cpython-312.pyc,,
spacy/matcher/dependencymatcher.cpython-312-darwin.so,sha256=Fm4nXvGhTVWIJ_JKpml6Ij7bfyuxDDXb4-P5tHMFM_E,384936
spacy/matcher/dependencymatcher.pyi,sha256=AO90GSUFcP6eHSKaoh3x8TWyZbkU8zBaDfkLPtesDwU,2125
spacy/matcher/dependencymatcher.pyx,sha256=MUDXuUu4zIcmWcX3_oJ-eMqrZUf9x3akpvm3jXcICk4,18210
spacy/matcher/levenshtein.c,sha256=wfzEpn5CwFIQEZA-SOALPl-Gx937kpyZgyGAEbWZC-E,344916
spacy/matcher/levenshtein.cpython-312-darwin.so,sha256=f2RoahQ5rh-iMhrH22seMqf9S9W40KSeos75igI0hQw,83008
spacy/matcher/levenshtein.pyx,sha256=NxIg4bxQkt0uM47Oz6RogAg0OfqskcYVSLvHWRBp_Cc,902
spacy/matcher/matcher.cpython-312-darwin.so,sha256=WJUQKayVncKXS3eecZxKVIn9Wnoc6tvKyrJ9vfLkTL8,524768
spacy/matcher/matcher.pxd,sha256=OiWeA-bA3Agkh7yhInq_YIx61zTcTilufEqxkKalY_U,1530
spacy/matcher/matcher.pyi,sha256=v8wAUi5Lvr5f78M37ANCUEkbvlUTY6ELt12N8IfvH-c,1874
spacy/matcher/matcher.pyx,sha256=l0IMqF4xtXBJ68HCsB4ydvAwbYBwetI6mbfZWnfor6o,50272
spacy/matcher/phrasematcher.cpython-312-darwin.so,sha256=QBUt2ztsfmJB-LsENwbUUgQiMafiQLlE_DME7mM0xtw,285984
spacy/matcher/phrasematcher.pxd,sha256=VJedr5q0bU_zjsIFbIzigOfswabn3wt7IlYuL2KYATo,555
spacy/matcher/phrasematcher.pyi,sha256=rNjuBFV91Ft8WlT7fWUmJyM4JTNjD8KmwYJr0ZljyiY,1045
spacy/matcher/phrasematcher.pyx,sha256=3RlL9Xo85g5zT8B-ICKFqM_S9qzre3XEAv0sSIB8K8g,14271
spacy/matcher/polyleven.c,sha256=jWzJWh5EwzTDpcH_4wEffo16Cv6ic0XXaafbfRarzgo,9571
spacy/ml/__init__.py,sha256=R9MMaM3qnMfl7dA9jARv4lpih9DrKtK71a1KdJdFl8U,109
spacy/ml/__pycache__/__init__.cpython-312.pyc,,
spacy/ml/__pycache__/_character_embed.cpython-312.pyc,,
spacy/ml/__pycache__/_precomputable_affine.cpython-312.pyc,,
spacy/ml/__pycache__/callbacks.cpython-312.pyc,,
spacy/ml/__pycache__/extract_ngrams.cpython-312.pyc,,
spacy/ml/__pycache__/extract_spans.cpython-312.pyc,,
spacy/ml/__pycache__/featureextractor.cpython-312.pyc,,
spacy/ml/__pycache__/staticvectors.cpython-312.pyc,,
spacy/ml/__pycache__/tb_framework.cpython-312.pyc,,
spacy/ml/_character_embed.py,sha256=xCrSi3zhBC54Z_bJd6Crv2M4fq0lmfTolBoTLUcQsZA,1955
spacy/ml/_precomputable_affine.py,sha256=1VErNk7B1ciU2E_2fZlsfPvjMgPpT8VGr9R2khNx_Qw,5785
spacy/ml/callbacks.py,sha256=wGO1Slq3e1Hwkf8oJdJI4rLqA3_BC3ZI8TUi_IYGi8w,3667
spacy/ml/extract_ngrams.py,sha256=RzEUEym18f0_U6R1cSR9MROiWhaIpfAlsPHWCfOgZNU,1147
spacy/ml/extract_spans.py,sha256=qeYQdySiwct8wjAvgJFPcDkL_OhPMZ_TybuoW2cWqUs,2261
spacy/ml/featureextractor.py,sha256=Cnop2J_qtKFh4e7Rw6WrD0Iyzs1L2SnsUDE9bZvMahc,959
spacy/ml/models/__init__.py,sha256=vz510ME9m4cfzGyZ8H9iKxigyJTmLkAb7MWj3yBUhSc,259
spacy/ml/models/__pycache__/__init__.cpython-312.pyc,,
spacy/ml/models/__pycache__/entity_linker.cpython-312.pyc,,
spacy/ml/models/__pycache__/multi_task.cpython-312.pyc,,
spacy/ml/models/__pycache__/parser.cpython-312.pyc,,
spacy/ml/models/__pycache__/span_finder.cpython-312.pyc,,
spacy/ml/models/__pycache__/spancat.cpython-312.pyc,,
spacy/ml/models/__pycache__/tagger.cpython-312.pyc,,
spacy/ml/models/__pycache__/textcat.cpython-312.pyc,,
spacy/ml/models/__pycache__/tok2vec.cpython-312.pyc,,
spacy/ml/models/entity_linker.py,sha256=I5yPX7u2ibAGF1L0JHR7eitKrVwzjPKPZul5DM-MB4k,4037
spacy/ml/models/multi_task.py,sha256=DDC3kfjI4EtpCSOnP5cxDkbRj9vYh1J4YsgKhUN1gJg,9164
spacy/ml/models/parser.py,sha256=Ar1B4gt4Ipu_ZXfKoBl6oeOrHpyDwUIRRBYM2Begf-w,6839
spacy/ml/models/span_finder.py,sha256=ZsoZ55bcZSw4jUMWTBArSY5cu05BD8po26NXGdz4LSk,1183
spacy/ml/models/spancat.py,sha256=4mas92YjdCzh0qxzauEAmao-Ji0vIQtgVCErYVinYjQ,2244
spacy/ml/models/tagger.py,sha256=Z-rnomjdaPgTDIZwAAwyh92mKnYzwWrHITqvmGzvQrY,1210
spacy/ml/models/textcat.py,sha256=PLlUkf6eUfHwi712cuIa-_6in9rIjJL8ZC7yTiZ4wXw,12787
spacy/ml/models/tok2vec.py,sha256=JdQGAFbyxh-0eUCrqRErYyDi0naIfOqnh-EvSwvXCKI,13655
spacy/ml/parser_model.cpython-312-darwin.so,sha256=6kOlx26_g0PzbtW-aLg3IOftHr-1G9ZWbvcHOg7Jf3Y,364864
spacy/ml/parser_model.pxd,sha256=3SiATBo_XrZf6ViccRwxUCbS6kYfm1joekd5NYO9uTM,1176
spacy/ml/parser_model.pyx,sha256=xj_Slk6nWNRpR9o05ZdeJPOMrtmgVyCcD7m2t916EYY,18497
spacy/ml/staticvectors.py,sha256=d0qnTXxKcWxA5ae4BofvJgY7kcMAcvA8Xd8TJ25Im3w,4206
spacy/ml/tb_framework.py,sha256=i-D9vi_vo-iUt6EHNIyY2A0Un39clvhvF_RVdbrUThc,1420
spacy/morphology.cpython-312-darwin.so,sha256=uaz9fYHxdX_2sjrGxCkqhzG-0pD301PRIC4r0R1lOr8,149296
spacy/morphology.pxd,sha256=hOgNmq-2MOI5j_mCWSNDXdFbUY2fgAEsVGB0ZJHZqj0,824
spacy/morphology.pyx,sha256=nQG9ROKEXkm4bbRHTmGR4dMX0JiB2-RTjlxjU_yxNKc,8253
spacy/parts_of_speech.cpython-312-darwin.so,sha256=e8cx9ggOU3J1fWGJYa0MSjVFr3QaexNJ-_NldGQZ7BQ,77080
spacy/parts_of_speech.pxd,sha256=80hJvYVj5ra_b5jwIkjI_Aycq5u4zDRebsLuBsBLmXM,258
spacy/parts_of_speech.pyx,sha256=cSi8fDw4UYhnTYjjdb8_Nq9bayc03MJ3sOOOJqXJoX8,575
spacy/pipe_analysis.py,sha256=9SByPJV2uCiKim5ABJFunyJkJVgNcY1crsATdV5qybw,6242
spacy/pipeline/__init__.py,sha256=Gr8hICLOo8n4R8sNuDTPxgReco0EyzuVcdFSkjaU8Do,1279
spacy/pipeline/__pycache__/__init__.cpython-312.pyc,,
spacy/pipeline/__pycache__/attributeruler.cpython-312.pyc,,
spacy/pipeline/__pycache__/edit_tree_lemmatizer.cpython-312.pyc,,
spacy/pipeline/__pycache__/entity_linker.cpython-312.pyc,,
spacy/pipeline/__pycache__/entityruler.cpython-312.pyc,,
spacy/pipeline/__pycache__/factories.cpython-312.pyc,,
spacy/pipeline/__pycache__/functions.cpython-312.pyc,,
spacy/pipeline/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/pipeline/__pycache__/span_finder.cpython-312.pyc,,
spacy/pipeline/__pycache__/span_ruler.cpython-312.pyc,,
spacy/pipeline/__pycache__/spancat.cpython-312.pyc,,
spacy/pipeline/__pycache__/textcat.cpython-312.pyc,,
spacy/pipeline/__pycache__/textcat_multilabel.cpython-312.pyc,,
spacy/pipeline/__pycache__/tok2vec.cpython-312.pyc,,
spacy/pipeline/_edit_tree_internals/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/pipeline/_edit_tree_internals/__pycache__/__init__.cpython-312.pyc,,
spacy/pipeline/_edit_tree_internals/__pycache__/schemas.cpython-312.pyc,,
spacy/pipeline/_edit_tree_internals/edit_trees.cpython-312-darwin.so,sha256=89c4A6Rqz4bbjFeL6bSP1GqjdgXqYbiwtcUyohOnSjI,180944
spacy/pipeline/_edit_tree_internals/edit_trees.pxd,sha256=Nsv-pV-dqkZ35rvUQtMDbBFbmKzU5DGUHxP4NdosZ8M,3474
spacy/pipeline/_edit_tree_internals/edit_trees.pyx,sha256=taWPP9SOkV1VK0LtFw6C2VARciK-kQE973XyLDrrqMk,10663
spacy/pipeline/_edit_tree_internals/schemas.py,sha256=uQ45oc2E6L-asBqngZWUnnw9Ccl3O4hEqAccXNE-z9g,1669
spacy/pipeline/_parser_internals/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/pipeline/_parser_internals/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/pipeline/_parser_internals/__pycache__/__init__.cpython-312.pyc,,
spacy/pipeline/_parser_internals/_beam_utils.cpython-312-darwin.so,sha256=bnDrlSiicaq9WukOR7D1KP4ymKYZEl-7upLIWe9hlYY,359456
spacy/pipeline/_parser_internals/_beam_utils.pxd,sha256=KB9hB2HGe9gzrlHb_RcSo9I6Jsv95P6ruUoV_MfMjao,255
spacy/pipeline/_parser_internals/_beam_utils.pyx,sha256=Q3QTRY-AI5r_ZGCfW0XdpnMCPuFskM8LZjn7z1cijD8,11533
spacy/pipeline/_parser_internals/_state.cpython-312-darwin.so,sha256=H0pEYc5PJFybXGBz_qrW1uy3Xf0aQktdFoLbdgIVc0I,97456
spacy/pipeline/_parser_internals/_state.pxd,sha256=9jKe-43XYVHcgGAoZrOCOyUwSe4jXFdUYbG-0Qq2vgU,13059
spacy/pipeline/_parser_internals/_state.pyx,sha256=vgsVAWry2IrwgVJQDjxVtJCvfNfbtmCcLguNYC73weU,24
spacy/pipeline/_parser_internals/arc_eager.cpython-312-darwin.so,sha256=XYoE4Gh2AAh5vDBZFoHOtakEd3736DYp_3HoQ5uzrfA,457184
spacy/pipeline/_parser_internals/arc_eager.pxd,sha256=L_q0gvacB49IrF6FYIUKpw8o8MeLBsLHoGSKMa9iHuE,211
spacy/pipeline/_parser_internals/arc_eager.pyx,sha256=AH8CNwqalx7UUoVDcZBzfUP3JaCLSlL-gds12Mg_kkY,30648
spacy/pipeline/_parser_internals/ner.cpython-312-darwin.so,sha256=hdtnypEwgeEHpj4kVv2ydjTk1dHQ371S3UPR_Q7NlO8,374136
spacy/pipeline/_parser_internals/ner.pxd,sha256=TBYvh_dGZUZqkRX5xmRVo2wkNl_j30ejuFBCssrZFI8,104
spacy/pipeline/_parser_internals/ner.pyx,sha256=4qpmULdmqMNSqDRQroE4i9rwCvOtErJPH072ykFw6ps,23251
spacy/pipeline/_parser_internals/nonproj.cpython-312-darwin.so,sha256=ojiypALtDnk6gqCo79Nz-QSt1Y5eHxSk_-mJEk63VOQ,268752
spacy/pipeline/_parser_internals/nonproj.hh,sha256=PnDSpjpQahWmTeCIuVdFuR7DHIk1gKXBnPJrYoCI5YY,187
spacy/pipeline/_parser_internals/nonproj.pxd,sha256=hXG8ZqSzb2423bStsfti9L1nQ9Myn4MEvdlVAqfKUKo,134
spacy/pipeline/_parser_internals/nonproj.pyx,sha256=QembtMfndYXZ3BnNyeXKvkmXzEjP4tIorZXqp0VXyNQ,8661
spacy/pipeline/_parser_internals/stateclass.cpython-312-darwin.so,sha256=sIDUS1skUGkj9Q1hVU4dFQrjiIFZ9UxgoJnw1k5W09s,309968
spacy/pipeline/_parser_internals/stateclass.pxd,sha256=4_qBbTmzolm-Nsfmd2p16ZKsHqxmoO1Fi9bSfMPrgRM,743
spacy/pipeline/_parser_internals/stateclass.pyx,sha256=KF5sPRT-anDWxtY9Y3veS4WSMp0PPxD2RvkAbpzLh6Y,4623
spacy/pipeline/_parser_internals/transition_system.cpython-312-darwin.so,sha256=V_rMEn5UAJGlwJWV3XhBqr2zHOqSlt8X49aF3oXBngA,325816
spacy/pipeline/_parser_internals/transition_system.pxd,sha256=lMEMyQwFuQsjmG-CwlnatO-dATnIP7RVjd6H0CRJVSM,1802
spacy/pipeline/_parser_internals/transition_system.pyx,sha256=3XpPZrYfRxNf9ZYLIaZrZ_cdGLALSfWZabREO4vn8vE,9137
spacy/pipeline/attributeruler.py,sha256=Fao_fTaPm3mfvLQgSTnBTq4QQ8a7SQC8Tsl4lZx9l1g,13601
spacy/pipeline/dep_parser.cpython-312-darwin.so,sha256=h3YfAu3wyG1ZA6ENahuFUV_-1SEKLFl4L5QYZYYPh4o,302160
spacy/pipeline/dep_parser.pyx,sha256=3V3uVzu87ySpM58_5cNwVcfSEfXQZtsJdvdfb6TD4dg,5734
spacy/pipeline/edit_tree_lemmatizer.py,sha256=gSxxqOcQBSArXS-4tG3zYIonQ_oGKnxapxuwOsijeAs,14796
spacy/pipeline/entity_linker.py,sha256=oFd_RBChb2sxAJdu0nlao0j8uJQeVqC0sF9p3rmO9lo,24189
spacy/pipeline/entityruler.py,sha256=OlhSWIPvRP3VykGQmmTrb1406OZMEa5LUI32cqeVggY,20268
spacy/pipeline/factories.py,sha256=_IatVLVOU6ty4LKWvjUkDPoI85h5FNfsqsCrbh8Z9a8,26820
spacy/pipeline/functions.py,sha256=axVYWnyx4itlpNid5SmbEH-WB-62ZTlEojFjDhO8gDw,6610
spacy/pipeline/legacy/__init__.py,sha256=He1TJ1hGaGwjiz10OGn-CuhGeGUtYFL3qdVqiJfyAlU,74
spacy/pipeline/legacy/__pycache__/__init__.cpython-312.pyc,,
spacy/pipeline/legacy/__pycache__/entity_linker.cpython-312.pyc,,
spacy/pipeline/legacy/entity_linker.py,sha256=MekgxV-SuES6hrXiW0gwIZYjNIEl9kMpLVHa-43z0gY,18788
spacy/pipeline/lemmatizer.py,sha256=bdPLB8X8st3QlBmirnldwuGL9IRiSTQUaA-ujjZjnIk,11902
spacy/pipeline/morphologizer.cpython-312-darwin.so,sha256=9JUpP0QAyw-AoXcY4sVDYZnN25Pwuqm95FpRXUgUxHk,281136
spacy/pipeline/morphologizer.pyx,sha256=WMvD5Zryrp19v-RBYmjwO8pATEg3Uv8VA9UdG4OoFSs,12234
spacy/pipeline/multitask.cpython-312-darwin.so,sha256=PWh28y-dMDmBxNLb1AEfzk-hLgSimxwcEztxQxABsAU,174592
spacy/pipeline/multitask.pyx,sha256=JVvetZ3_hdBnHpCJosjcCVRyMdYmHrdOdDd8YQVjAWA,7404
spacy/pipeline/ner.cpython-312-darwin.so,sha256=jlKJUWH0FY_7jNUewA19ETRaSuyyMqUrDhl_EEysg8s,286200
spacy/pipeline/ner.pyx,sha256=uQ-IdDz2EPfUKoia63_CCOoXcTSaNJuA51JE68QLluY,4121
spacy/pipeline/pipe.cpython-312-darwin.so,sha256=lDlnlWlIcf3xLPgmTbMgLe_SIn4DYI5n9G0Pn1p_TPI,244472
spacy/pipeline/pipe.pxd,sha256=2SNXlXJH2cK5CsS5Z396NENL9KEIAmXks98hutFcEKs,42
spacy/pipeline/pipe.pyi,sha256=ZCNtVR46vuiSnGr188dghJSZi3LndQXswxg01ov6YBg,1250
spacy/pipeline/pipe.pyx,sha256=RW1I_IZ49Ix4cZ0ABd3pp8xa-pncklikbOrEgRI4ezM,5290
spacy/pipeline/sentencizer.cpython-312-darwin.so,sha256=9N10o6U88X3tROucylhAUlCtfVzH0YKzX-q_1wxTJNs,268320
spacy/pipeline/sentencizer.pyx,sha256=sns818eYiv_SVv3e09PgLdbT_0EbfDfVStDeX6bnYmQ,6542
spacy/pipeline/senter.cpython-312-darwin.so,sha256=LfoHqRXFMe6KxqSev6aPZV43BxzxIGiPrqQKtb6FVzE,261248
spacy/pipeline/senter.pyx,sha256=iYfe408iEvGg3gdM7Qipcj4GbeC42Vh11AvNWCQ3n48,6298
spacy/pipeline/span_finder.py,sha256=q5nnJSXbC_OYVP8uBNRwqWT8ihkR5Ws2BEmjN7hfs2s,10579
spacy/pipeline/span_ruler.py,sha256=5GuK16HOmSPlCUxFXgmoCsjBxRYfXtfFNJZnFoNmES0,18993
spacy/pipeline/spancat.py,sha256=dc1v0hBA-MFrY9WIypuvinbIuj_Aa17KEJ5yHUN0ICw,24991
spacy/pipeline/tagger.cpython-312-darwin.so,sha256=_vGhbbqM0GiLIP4iNQZOXhd6fN2Rk_dPztpxdxfMbbA,304240
spacy/pipeline/tagger.pyx,sha256=77k9lL8pKYBC3UlA_raDic4yg-4MvAqx400h5nFMrew,11465
spacy/pipeline/textcat.py,sha256=cx_0bbASFd-bIqUGUYYii4-QRwAG3rssZ9iou8SomrA,14038
spacy/pipeline/textcat_multilabel.py,sha256=dDdv429TcFcmILiafygNXk8rQTLb8zfDGipcn2ildm8,5578
spacy/pipeline/tok2vec.py,sha256=drdcrgglU_z9743cfQMX7RHyXip9pALEVCaKhVqm3-Q,13476
spacy/pipeline/trainable_pipe.cpython-312-darwin.so,sha256=6UFKim_atbxAz9Oa0aXwY2r9ZuKabq9CLZIf81Ys_Ps,333448
spacy/pipeline/trainable_pipe.pxd,sha256=RkF75dmfOieqFolQhg6hlP1Y9XBSvScC5ecx8j6jAaQ,199
spacy/pipeline/trainable_pipe.pyx,sha256=ClE6WMiqmtvuvpmm4VojkIiZAtskhhZfWBbs3A8akJg,14020
spacy/pipeline/transition_parser.cpython-312-darwin.so,sha256=WvL8UXix7dRTJKAffCOfCkim4an85uxb6yeuTOBmPds,487192
spacy/pipeline/transition_parser.pxd,sha256=Knv65l-56-OiyrWraU0qpb5pLshUteFJSHQBsUbJRFo,801
spacy/pipeline/transition_parser.pyx,sha256=675wBozFgf2WoNzlcQnu9R1lUxy_fVxr3bugBMx1cTQ,27442
spacy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/registrations.py,sha256=uwHYak6ovh3vX-zmVGy5YV_uq8U0-ToGKF67RCAHuOk,11371
spacy/schemas.py,sha256=a0idM3uZzIdXS6W3dQ2RlZr42TV7r-SPcIXoKd-vvR8,21703
spacy/scorer.py,sha256=Or6dg92jjDi7UitqitdDwNGtbsbRwj4gF39QwKQorpA,45768
spacy/strings.cpython-312-darwin.so,sha256=JbVisa1XrLhXbZJzeLio8CiYjny9vGoJQb9KYIv3cpo,179616
spacy/strings.pxd,sha256=9Ci8wD-0gvFF6bDzyJ6TP4eKt9Ii5EugeYhDA22Exwc,840
spacy/strings.pyi,sha256=ZeZnguPyp-X35p0BLJ1HVnvo-42KECHL6Yed-2Gwy6s,1098
spacy/strings.pyx,sha256=DzJ9gkvRyKgwQmRVkXHM5zJcEFdoRqOA5FkUI8_I-_8,14014
spacy/structs.pxd,sha256=V9jCLQktl-KVdljmdSci3igbuTqs774NGCpSshvP3kQ,2502
spacy/symbols.cpython-312-darwin.so,sha256=LCSqaYrAta4lOWNdlPMPb79Z9TZ8fOJxX9y_rgdUgQw,158192
spacy/symbols.pxd,sha256=4Pr_7zItSgTtSoy_XEQOXosMagwzwgKv2JrV9WhcoZ8,6943
spacy/symbols.pyx,sha256=gYZqC6MQ5z4PpiisC93pmIc2h1wT8DNiL1wLfYptQUc,14205
spacy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/__pycache__/conftest.cpython-312.pyc,,
spacy/tests/__pycache__/enable_gpu.cpython-312.pyc,,
spacy/tests/__pycache__/test_architectures.cpython-312.pyc,,
spacy/tests/__pycache__/test_cli.cpython-312.pyc,,
spacy/tests/__pycache__/test_cli_app.cpython-312.pyc,,
spacy/tests/__pycache__/test_displacy.cpython-312.pyc,,
spacy/tests/__pycache__/test_errors.cpython-312.pyc,,
spacy/tests/__pycache__/test_factory_imports.cpython-312.pyc,,
spacy/tests/__pycache__/test_factory_registrations.cpython-312.pyc,,
spacy/tests/__pycache__/test_language.cpython-312.pyc,,
spacy/tests/__pycache__/test_misc.cpython-312.pyc,,
spacy/tests/__pycache__/test_models.cpython-312.pyc,,
spacy/tests/__pycache__/test_pickles.cpython-312.pyc,,
spacy/tests/__pycache__/test_registry_population.cpython-312.pyc,,
spacy/tests/__pycache__/test_scorer.cpython-312.pyc,,
spacy/tests/__pycache__/test_ty.cpython-312.pyc,,
spacy/tests/__pycache__/tok2vec.cpython-312.pyc,,
spacy/tests/__pycache__/util.cpython-312.pyc,,
spacy/tests/conftest.py,sha256=lZaI7omE1V-O4UpwmS2pSDB1nbwfbs6QTYGExNlvImM,11906
spacy/tests/doc/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/doc/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_add_entities.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_array.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_creation.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_doc_api.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_graph.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_json_doc_conversion.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_morphanalysis.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_pickle_doc.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_retokenize_merge.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_retokenize_split.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_span.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_span_group.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_token_api.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_underscore.cpython-312.pyc,,
spacy/tests/doc/test_add_entities.py,sha256=2WNwaoTZetGqEJtG9KKE7dfJX2k51mzIiuKABYpsdas,1804
spacy/tests/doc/test_array.py,sha256=cs-tgb8DSoMRas8W05RZUQEu4hNipq_flddCEiqSe3Q,4988
spacy/tests/doc/test_creation.py,sha256=NEVqgfcCTaKY2EBYsuWfHHOot3q5bjlYej2-Erd1V7Y,2651
spacy/tests/doc/test_doc_api.py,sha256=WXxLYMPeq5pEcEO64jnKj8Gzeex0vhoZz0lXbbxf6h0,35921
spacy/tests/doc/test_graph.py,sha256=bxGLm5epbVLPDhN8KSu52eCFFGDg93FqyvMvMtPgqzs,1819
spacy/tests/doc/test_json_doc_conversion.py,sha256=oEMRePipxX7nRBQJQ5xQgS1qE5afrzULDoQ9XHnEsps,13660
spacy/tests/doc/test_morphanalysis.py,sha256=jX_rtdIQL114r9CeFsqiM8UEIKuGTRbgav0_4a8waew,3145
spacy/tests/doc/test_pickle_doc.py,sha256=VXs9ZzTQyaM7PN4EWVThCT1ryHBY7-4HgihZkOJ0gVo,1470
spacy/tests/doc/test_retokenize_merge.py,sha256=wjPnS2pT8FboW60xiCEh3enhz8h9PJ8rVmhfOg2qBzM,18956
spacy/tests/doc/test_retokenize_split.py,sha256=Lk93mjC4cbtXvSGT2M67RSlDe-Of3q_WMkSjimetxqE,10937
spacy/tests/doc/test_span.py,sha256=uea5Xxz9hqua52A-l3TdAOQqQEQ0NPpNn8yE7A6BKDM,25676
spacy/tests/doc/test_span_group.py,sha256=luczRoe-30DPoLNogs31Z5uoGesug4jpX3nc2qZXTmA,8667
spacy/tests/doc/test_token_api.py,sha256=XFe7tDuzmrPaQU5zoJ3gM0e2VkMbZykbNDoObjUeaeQ,11414
spacy/tests/doc/test_underscore.py,sha256=f6DgJftA6K6CvUiFOLzYsjvFJw3zQCWkhy817axZVZs,5565
spacy/tests/enable_gpu.py,sha256=LDIdt1gbkbyGpGATvzndS8rjGuzzU8Z6ikRegPLABxk,45
spacy/tests/factory_registrations.json,sha256=TeQHkvtGdv0OFjbe8RggnaZUZ9c--S-Zfzvn296cyFU,3490
spacy/tests/lang/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/__pycache__/test_attrs.cpython-312.pyc,,
spacy/tests/lang/__pycache__/test_initialize.cpython-312.pyc,,
spacy/tests/lang/__pycache__/test_lemmatizers.cpython-312.pyc,,
spacy/tests/lang/af/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/af/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/af/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/af/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/af/test_text.py,sha256=KO2NoeJclewrlo0zfugUVX-0WKMlWwnWF2-ULaw1AY4,931
spacy/tests/lang/af/test_tokenizer.py,sha256=SUgFHIV2OjFPX27wby--LetE3a7BguAqjAO1s7Vb7dw,710
spacy/tests/lang/am/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/am/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/am/__pycache__/test_exception.cpython-312.pyc,,
spacy/tests/lang/am/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/am/test_exception.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/am/test_text.py,sha256=wTldMQWlst0V7MxMgPnU3iNufStJTq6RbtLWh6nfwzM,1837
spacy/tests/lang/ar/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ar/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ar/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/ar/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ar/test_exceptions.py,sha256=q3QloV3I0NEgI_Y_qx336yq6K6_wsqTniMCVOQ0J4-o,621
spacy/tests/lang/ar/test_text.py,sha256=NuzMwErxsFPs83Y60A5XIj8Hx7GrrHz-ui0BJJ0x9xY,821
spacy/tests/lang/bn/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/bn/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/bn/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/bn/test_tokenizer.py,sha256=-5tft6bwrTxHZHTSX1zpPBvS9dEpnRBUvF6cyrWrKec,3622
spacy/tests/lang/bo/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/bo/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/bo/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/bo/test_text.py,sha256=lED8_7Q_dzCV2VV214uUxtziv-6pJ8LFefGKYZnUts8,517
spacy/tests/lang/ca/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ca/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ca/__pycache__/test_exception.cpython-312.pyc,,
spacy/tests/lang/ca/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/ca/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ca/test_exception.py,sha256=ipInozr3JR5_oSDC_c2qcvBW-jeDiAH0DWks_8pK8U0,621
spacy/tests/lang/ca/test_prefix_suffix_infix.py,sha256=SmgJ3RXkHZQmjRQC42Mg7OkyiskGW93MzTpjnSrptOk,493
spacy/tests/lang/ca/test_text.py,sha256=jx2B6IWy_a0wDvIYJPwTTb4bWejTOpHy7IMNEbRA0nU,1901
spacy/tests/lang/cs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/cs/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/cs/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/cs/test_text.py,sha256=crgPQetf-qAQURSWkO6iCbZWyLR1gut0Xl4Nf_kqWiA,510
spacy/tests/lang/da/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/da/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/da/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/da/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/da/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/da/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/da/test_exceptions.py,sha256=gPRm1clvKNulUSKdcprcrEWEtpbUWgd4Y5ywUBKyiX0,1824
spacy/tests/lang/da/test_noun_chunks.py,sha256=taHukk4ziHrT7JC7AZydDOfhgMIUaBK6DocyIHJhFvE,2064
spacy/tests/lang/da/test_prefix_suffix_infix.py,sha256=hpY6bo4dwrOPkqOsXZlEMWPhgdi6_rlj6-hsbd_2XYo,5423
spacy/tests/lang/da/test_text.py,sha256=e2VuAOeoko9MORTfuxHONVetxVz_D9x8I5UyOQQI_7g,1220
spacy/tests/lang/de/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/de/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/de/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/de/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/de/__pycache__/test_parser.cpython-312.pyc,,
spacy/tests/lang/de/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/de/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/de/test_exceptions.py,sha256=jTnHmb0a3zKsUpKntE9068o0DvQCjWDkEoOlJ77m58Q,598
spacy/tests/lang/de/test_noun_chunks.py,sha256=21RKhw8FL7o7QMNyC-Tku6B7nZqxVKkL2mnS-9D-PkE,266
spacy/tests/lang/de/test_parser.py,sha256=S_hGyCbr5Hoe9pc7TZ-Maz80QojcGenqLevkzNYVO44,1188
spacy/tests/lang/de/test_prefix_suffix_infix.py,sha256=xilwQy-tuNxm-HFi3KSn7V1aURxtcOfQmwKlNMX9HYQ,3395
spacy/tests/lang/de/test_text.py,sha256=hzxKqSCDIhIxn3C3EpGslw_2enX6pgr6nFvMPH8Ou9Y,1505
spacy/tests/lang/dsb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/dsb/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/dsb/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/dsb/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/dsb/test_text.py,sha256=5ayXZxVMDL9WCuYp19oc3OL-_1a_XfWME4k5VcBq5MY,560
spacy/tests/lang/dsb/test_tokenizer.py,sha256=Miahv92EenmiGe2FUbuGaHZYe_SxlpUauIYZJjcomtM,749
spacy/tests/lang/el/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/el/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/el/__pycache__/test_exception.cpython-312.pyc,,
spacy/tests/lang/el/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/el/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/el/test_exception.py,sha256=iaM9ab2vlFek0njEjnMwxpIyuxU2pGU0tEvv91JHaQk,514
spacy/tests/lang/el/test_noun_chunks.py,sha256=dlVYwbVxpXQ0W3L5w3x1WE9DsKKRTLaKYvgqKCNs-jM,306
spacy/tests/lang/el/test_text.py,sha256=ou0AwLkHkVo3YdpqHVfaDTVOYoF-ZIz0mDTgWUrdcDA,1768
spacy/tests/lang/en/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/en/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_customized_tokenizer.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_indices.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_parser.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_punct.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_sbd.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/en/test_customized_tokenizer.py,sha256=lUeotC_it87_Xo8WBWoyRN-u205-I7BMAbw-v2UK_x4,3778
spacy/tests/lang/en/test_exceptions.py,sha256=rLbogxx45fvlsiqkr0BMOfFP0Yb52tzGdvX6uWU663g,4158
spacy/tests/lang/en/test_indices.py,sha256=jC3CTGq_GTjRcRBvXmUU_CKzOGSRGRrdIGTJF8l4khI,723
spacy/tests/lang/en/test_noun_chunks.py,sha256=u-xQo4vx0LkN082GCXIXYIsT6hdxJOQRkk_7LRZodyM,1549
spacy/tests/lang/en/test_parser.py,sha256=Iot1x7wL20LmDK1g9458RvqxCGzC_cajZyb9QwBLvbM,2954
spacy/tests/lang/en/test_prefix_suffix_infix.py,sha256=YoSd1lROAkGp2w8bHeEdG9rsPX-VNdcr7Nc-3uKXF40,4254
spacy/tests/lang/en/test_punct.py,sha256=NRXppqgUi2hHm59oHKdqc_plsY_GAHpciy4pXF_1noY,4422
spacy/tests/lang/en/test_sbd.py,sha256=4rYk4pWDptPmkAR5QERsrg8hBsbZr1bY20VMqr2bv7A,1708
spacy/tests/lang/en/test_text.py,sha256=t-g0aBSW9SPCCc6o8HJQT9M31qYGyBiaV-592GY1C04,1963
spacy/tests/lang/en/test_tokenizer.py,sha256=Z45YnK7neKCR-lqdjY-NS07xS09ScF_-3qaBEYiYXYg,5938
spacy/tests/lang/es/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/es/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/es/__pycache__/test_exception.cpython-312.pyc,,
spacy/tests/lang/es/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/es/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/es/test_exception.py,sha256=fTSoQ-3efHlvuKz2xVvqRLII34ZMwbdF5LTXybnWnQA,549
spacy/tests/lang/es/test_noun_chunks.py,sha256=SxqXbiM68Ws0-v4qvgrjwm-e3TvX4mMgLXMlA_Tx3wQ,9967
spacy/tests/lang/es/test_text.py,sha256=1xVvaIHlDyduCCZSpEqWetMbchClsDrftuaLzZXYC4c,2026
spacy/tests/lang/et/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/et/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/et/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/et/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/et/test_text.py,sha256=ZkxP5cwXo1YIGXsRcWZs1PT3NhrGc35Hdyi5SAU_0Ug,933
spacy/tests/lang/et/test_tokenizer.py,sha256=V6YYMfHoEmk7C30vWQ-XMcHRW5R7frUNNtJzWoo3Afg,737
spacy/tests/lang/eu/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/eu/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/eu/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/eu/test_text.py,sha256=rnBgO2l4oYd1ZLzSvgHaNeOfEcUrFc19iXcLcyK29AY,488
spacy/tests/lang/fa/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/fa/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/fa/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/fa/test_noun_chunks.py,sha256=6EmEoBZI7pOWj1D9SHLmZuR717xEBPjiVNKSCNur2fA,296
spacy/tests/lang/fi/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/fi/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/fi/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/fi/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/fi/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/fi/test_noun_chunks.py,sha256=Am3Gu81y4EkWI97K2Leu18x2kAhikMSsdQBTpPxBjnE,7258
spacy/tests/lang/fi/test_text.py,sha256=Ta8roiQl0MqL_-E6Aizv9H8BWd1RVuiNmDpx9G_70s0,542
spacy/tests/lang/fi/test_tokenizer.py,sha256=n1pUgD4VlYlNF81SeWbou2aDLvYOzRarF5pLe_mLFHk,2899
spacy/tests/lang/fo/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/fo/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/fo/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/fo/test_tokenizer.py,sha256=vmx8E9UaB71W6n8ZKA6xLpUN82TotfLVn8RszEi8CL4,1515
spacy/tests/lang/fr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/fr/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/fr/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/fr/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/fr/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/fr/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/fr/test_exceptions.py,sha256=b_T54oEgRzVxHmegoXJ7576rxE2oUk7wCyvFdW0RimE,2219
spacy/tests/lang/fr/test_noun_chunks.py,sha256=TPTdwgYRpeoyHeMdHFlcX-6VIjPej2IyTQwYa3odKok,8354
spacy/tests/lang/fr/test_prefix_suffix_infix.py,sha256=Z1hMy60pKHuy0XXR8sTjHUMiUQ6NYiImiRP5bU4bHEw,773
spacy/tests/lang/fr/test_text.py,sha256=ENe7tlspBOFLaYvRYT6DgQyjM5HdvlSKRXiw5oMc8r8,1012
spacy/tests/lang/ga/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ga/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ga/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/ga/test_tokenizer.py,sha256=IaK2LjfqT1Ow2eLQuyOeKL83665PAwxNRTASTfLOJJ4,684
spacy/tests/lang/grc/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/grc/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/grc/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/grc/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/grc/test_text.py,sha256=gTot2Gu8sMYOR5ic3qAhYeU40OLBJSj9Tx31YGzzc_E,543
spacy/tests/lang/grc/test_tokenizer.py,sha256=OKCpVNsLaUCJFNBDjsUPWvF1k-l5j2gAF-ZcjNl-wAk,1326
spacy/tests/lang/gu/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/gu/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/gu/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/gu/test_text.py,sha256=BryXo1DYk_YjXgY6tsWxupmUjQXaoXVRrB4nVp9eyPY,685
spacy/tests/lang/he/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/he/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/he/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/he/test_tokenizer.py,sha256=hA8oCh_KEg-9wdrg-KapoXL4evwoMPKKH183NdLEJpY,2256
spacy/tests/lang/hi/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/hi/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/hi/__pycache__/test_lex_attrs.cpython-312.pyc,,
spacy/tests/lang/hi/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/hi/test_lex_attrs.py,sha256=KTB3q4yk2x3o6-61KDKl6gI2iOsJaf7gEX0JmAgKrHY,1842
spacy/tests/lang/hi/test_text.py,sha256=Jra4nQzqfqZ-p3MW3aG4-W5ApNPZ1v007YW2lbK0KN8,401
spacy/tests/lang/hr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/hr/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/hr/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/hr/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/hr/test_text.py,sha256=4AarCtIm9TQF4TbHTqwWqhuIeG9MEgHlxQbEEaHQ4h0,954
spacy/tests/lang/hr/test_tokenizer.py,sha256=T9z9BdhBVtmBGs6_TC54z_Dspm2er4fPQf0W54x0nS8,801
spacy/tests/lang/hsb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/hsb/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/hsb/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/hsb/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/hsb/test_text.py,sha256=Q0MtVpLpd5yvyt7J37_FM7j1HQ_OsPcWkRHVtTLRg_M,566
spacy/tests/lang/hsb/test_tokenizer.py,sha256=QeFOGXPdTkc-qH8gLJHiRzk8fj1Btfk-1UiF7B7YY6Y,866
spacy/tests/lang/hu/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/hu/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/hu/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/hu/test_tokenizer.py,sha256=kYIIULvu8acNDc0sFIIaGzYPW2UCxLpSBTsqLiZ6iZo,14491
spacy/tests/lang/hy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/hy/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/hy/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/hy/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/hy/test_text.py,sha256=DqmjJE_DOJ5BURhqoEH5p2d4JwqL32MEUMqa4Iih3xI,211
spacy/tests/lang/hy/test_tokenizer.py,sha256=v3u2ZfWTAxNkmdWOIYmUaJvSnyok3kHLcs6qHoy8wek,1350
spacy/tests/lang/id/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/id/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/id/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/id/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/id/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/id/test_noun_chunks.py,sha256=UB4HtwJ2Z1esEX59xJaCIbIo___xdvM1ofFeXrLUkBc,256
spacy/tests/lang/id/test_prefix_suffix_infix.py,sha256=ZpMoC8qgsUeTxVWn2NIS1FnyYUBNvvB4AwjVvZY32Oc,3492
spacy/tests/lang/id/test_text.py,sha256=bj9z0-JNs_t23_78pYLjBPKmMqMqiBvQSypw0uDXE9w,206
spacy/tests/lang/is/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/is/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/is/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/is/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/is/test_text.py,sha256=yEP0g6Y18Sl4e5abxh3t8VNTRvxi6Y3zXTYcq9EGwSs,980
spacy/tests/lang/is/test_tokenizer.py,sha256=zjr8IzHoHhBuwSeLI_zmJjk-PrP-82WfcUkW65NeWRc,783
spacy/tests/lang/it/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/it/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/it/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/it/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/it/__pycache__/test_stopwords.cpython-312.pyc,,
spacy/tests/lang/it/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/it/test_noun_chunks.py,sha256=_eWqQraalCKjxDue2SLQyb_H765_Zb9WzFOYeCprvPo,8640
spacy/tests/lang/it/test_prefix_suffix_infix.py,sha256=PKdDsBDlSzKFStsb1L06t-PjYTa3MZ7Lo8cz2l6BTcM,359
spacy/tests/lang/it/test_stopwords.py,sha256=xOPECnG05oahFZQgtFqnZNwZaJ5bhynqjCSpWAd_kV4,449
spacy/tests/lang/it/test_text.py,sha256=bTr4xutefXBSY0Jo60ItA7j-siIe5utSaACF5a-2t4w,411
spacy/tests/lang/ja/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ja/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ja/__pycache__/test_lemmatization.cpython-312.pyc,,
spacy/tests/lang/ja/__pycache__/test_morphologizer_factory.cpython-312.pyc,,
spacy/tests/lang/ja/__pycache__/test_serialize.cpython-312.pyc,,
spacy/tests/lang/ja/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/ja/test_lemmatization.py,sha256=BXomwQpSrlchcznz_r76fQp9qboKvRNtqAXM5t-Y0YY,741
spacy/tests/lang/ja/test_morphologizer_factory.py,sha256=PkjPUNtexCKcltKTzdZAUCYAbmKU2KSai487TusHxU4,244
spacy/tests/lang/ja/test_serialize.py,sha256=RWIhG-M-MJsI8t0SQBJdjWcXWQOT4nKKiC_1PDi3ddA,1307
spacy/tests/lang/ja/test_tokenizer.py,sha256=pNbddj2B1QFbVgkEz094Futcdl4uM9jJ_3CBlQaOsYg,7980
spacy/tests/lang/kmr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/kmr/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/kmr/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/kmr/test_text.py,sha256=FP8Wq_U4NkmNlaDjXb6iCDwAgJ5Pc6-aTAZesCl_O-M,502
spacy/tests/lang/ko/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ko/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ko/__pycache__/test_lemmatization.cpython-312.pyc,,
spacy/tests/lang/ko/__pycache__/test_serialize.cpython-312.pyc,,
spacy/tests/lang/ko/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/ko/test_lemmatization.py,sha256=w6EOM3ksZcjWZu3kykRwu7pWBLnBp1NyVNaUyWgdZzk,361
spacy/tests/lang/ko/test_serialize.py,sha256=YHIMKDy8lbDUlMMO4OaYJ9theepfHxBvdakTnh8c5bc,713
spacy/tests/lang/ko/test_tokenizer.py,sha256=JIZHsMxomqaNkI78crINZgxFn6YRq8tofr_jtBRFxTc,2893
spacy/tests/lang/ky/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ky/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ky/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/ky/test_tokenizer.py,sha256=gdzIJIsFLf2xr-ehrch1MDr1C1fwZUn6UatEQcWXnms,4013
spacy/tests/lang/la/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/la/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/la/__pycache__/test_exception.cpython-312.pyc,,
spacy/tests/lang/la/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/la/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/la/test_exception.py,sha256=QWuc6A97eqddnJzjlNpyrALFQGpD02FtQhrSZYDpDIQ,236
spacy/tests/lang/la/test_noun_chunks.py,sha256=pVO4x7YLrEETPOIpAvJ_vfBfUQZCV3N-nSDTgkP-Peo,1628
spacy/tests/lang/la/test_text.py,sha256=RLg292uFF2avsrHacvyD7gKLdhiVp-yaWzBmE5bbyYE,804
spacy/tests/lang/lb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/lb/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/lb/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/lb/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/lb/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/lb/test_exceptions.py,sha256=CSYxd_iKek2MzwcQeI-ePQMHiLCsnUgJy0NIo33E3tE,590
spacy/tests/lang/lb/test_prefix_suffix_infix.py,sha256=SLIBNk2WOp8uMIYgEox2SCfhh-xMdhVhazKF3MfhwWw,584
spacy/tests/lang/lb/test_text.py,sha256=RlOpfAJQE_tJwC1UYjftnNj3spqkDxhvjPG48forDqQ,1302
spacy/tests/lang/lg/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/lg/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/lg/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/lg/test_tokenizer.py,sha256=swcG7azpJ6CFxasFlSXlAKL_qHT7cZUMdGUXD4pyf_M,449
spacy/tests/lang/lt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/lt/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/lt/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/lt/test_text.py,sha256=ARU2XeS1QU92QOQZcGDeA6QlVXEfNxTCWvtq1V-OSDw,1644
spacy/tests/lang/lv/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/lv/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/lv/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/lv/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/lv/test_text.py,sha256=l-k6wSW6WOEySYqjZuTrPdlzUD7e-LPJnhfz5J6gV70,1018
spacy/tests/lang/lv/test_tokenizer.py,sha256=xc3WvT1o_LcMgAyK1mKR6H7WjTsPqny571N6WukmZbo,778
spacy/tests/lang/mk/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/mk/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/mk/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/mk/test_text.py,sha256=j0mVwVLSAxioNsmkyIZkMEXiPAoJByM-0u5Bq50mqS4,4353
spacy/tests/lang/ml/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ml/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ml/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ml/test_text.py,sha256=bKW2VZXulf7IYii-YsbesbOk55PMdcaQOGNzzX8eZSQ,1075
spacy/tests/lang/ms/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ms/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ms/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/ms/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/ms/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ms/test_noun_chunks.py,sha256=bgNcm6uSjHtRikywdPw7uZlqVzUrK84f8C62rmJ7WmY,256
spacy/tests/lang/ms/test_prefix_suffix_infix.py,sha256=RJ5w0JYsyLLfpZQZgcsejSaIeFUbFICvNqAvbnMPmoQ,3512
spacy/tests/lang/ms/test_text.py,sha256=X6NtG1tP3uKDTaYcGzWttQa-DFcfq7kiZCn2LX_Zd0A,206
spacy/tests/lang/nb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/nb/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/nb/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/nb/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/nb/test_noun_chunks.py,sha256=f6k0bMzynBzLIrAt-nXYt8h3oqIF1BpD-2ULypS4U9Q,277
spacy/tests/lang/nb/test_tokenizer.py,sha256=HGvzPYbhN80Wzsj0pyFiIKJMcJ2eF3-8oAm-uuRFquE,629
spacy/tests/lang/ne/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ne/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ne/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ne/test_text.py,sha256=No3TyHdAS5Ybe0rV7OOHjqOwfRatygU10ktnXZsZGxs,783
spacy/tests/lang/nl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/nl/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/nl/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/nl/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/nl/test_noun_chunks.py,sha256=Qi-CR53CnjRaKT6WeSKCafKCZAi10d37OtyoPE5kFCA,4304
spacy/tests/lang/nl/test_text.py,sha256=lyfa-ZRJyv4uuH5tLoRZOMdlkwh7-zfAc0OL_hWIsJ0,694
spacy/tests/lang/nn/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/nn/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/nn/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/nn/test_tokenizer.py,sha256=Ut-A9vei81yZuUj598XCWflJZRui1irkGvUPMPousX4,1796
spacy/tests/lang/pl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/pl/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/pl/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/pl/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/pl/test_text.py,sha256=QEFwbg6SKTsqb8AOdp6PMMxM5JT7DXSSDJezH5qI2Og,523
spacy/tests/lang/pl/test_tokenizer.py,sha256=8CwaguWR33DBvNkUJS7SRGNYK98nQ0j9JvKNkzyFhB4,555
spacy/tests/lang/pt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/pt/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/pt/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/pt/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/pt/test_noun_chunks.py,sha256=KnVkp8c9QGM926klx0P6pALYwwHciodlJVxYxACD2G4,7749
spacy/tests/lang/pt/test_text.py,sha256=9hF4_kmpuZl10IjQWkOFQlu2bCHel_ARAF9Db7eUGaU,220
spacy/tests/lang/ro/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ro/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ro/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/ro/test_tokenizer.py,sha256=ajJmmQUfIHMLRwryGLSgiJtidNjkwZ1K69I3FqnNmd8,733
spacy/tests/lang/ru/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ru/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ru/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/ru/__pycache__/test_lemmatizer.cpython-312.pyc,,
spacy/tests/lang/ru/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ru/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/ru/test_exceptions.py,sha256=YFYujZL-7xUlv0HRq9lPx8OVp07ZB1H1C2TP59gQp5o,353
spacy/tests/lang/ru/test_lemmatizer.py,sha256=kMS_ZX9hlm8mWxwOBNbimhVzrVF2lKlQ51XoFdm1Hw4,3958
spacy/tests/lang/ru/test_text.py,sha256=VDPdZsZyJbZ2Yzr0Gf81Kcrju6rAtilCL67Jk-dXdjM,221
spacy/tests/lang/ru/test_tokenizer.py,sha256=UBcWCpcnbQEqsIxCaPGH85jBv1meF_t-X8xUTxRzKsQ,5983
spacy/tests/lang/sa/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sa/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/sa/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/sa/test_text.py,sha256=mzwNhfT4pW5Hh1x5AIrAO0PYDGmAUkXTcpxB997FkYk,1297
spacy/tests/lang/sk/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sk/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/sk/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/sk/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/sk/test_text.py,sha256=xzhPFgWflGct8ng-k9UjPCG5l8huzIjDvGNxbjr_Pq4,1485
spacy/tests/lang/sk/test_tokenizer.py,sha256=dmA5H_MQXkIlF-0pN7Mp1up2g6obRTNorzIbQIFPFEY,453
spacy/tests/lang/sl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sl/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/sl/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/sl/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/sl/test_text.py,sha256=hXEw0zrVuF8_Yhi3d4BdZ0gumOi-5hvPsZXq0CZxxpc,993
spacy/tests/lang/sl/test_tokenizer.py,sha256=zbpXzV2Wde0aaQz2Zjpd9hWMnzQRXJMi1Aol6uin-ks,831
spacy/tests/lang/sq/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sq/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/sq/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/sq/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/sq/test_text.py,sha256=pZ2uB6fMgQH6mGPBoEWWDHRUgyf8Un4cVEEV9_nY57k,1178
spacy/tests/lang/sq/test_tokenizer.py,sha256=UY4CKSzgvCBY9W4aB2B9uMdXMLhTfehZPrkvPXYJ270,817
spacy/tests/lang/sr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sr/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/sr/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/sr/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/sr/test_exceptions.py,sha256=4PPG8TwqijR3j5BzShFNUUaPca0j3wKjrx00wLRGEFc,510
spacy/tests/lang/sr/test_tokenizer.py,sha256=T-9KbB52RCJemd-uqY001MlYnGlPrSHnqFysOBRw2Wk,4301
spacy/tests/lang/sv/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sv/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/sv/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/sv/__pycache__/test_lex_attrs.cpython-312.pyc,,
spacy/tests/lang/sv/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/sv/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/sv/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/sv/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/sv/test_exceptions.py,sha256=JMP6SmY8wN_65gfQ6_1J-Hnh-tCrW9hTOa6dLFmy3TE,2453
spacy/tests/lang/sv/test_lex_attrs.py,sha256=fJTUDUEP_Wf0rer23kBu2wOFJd7SFkOriQyYNTDZLq4,684
spacy/tests/lang/sv/test_noun_chunks.py,sha256=9C7t3R7fqv-udBongKYJocKjlvNIl_ctEJh-6oIfZM0,1858
spacy/tests/lang/sv/test_prefix_suffix_infix.py,sha256=kwhufYaG5ncdnd_4gzHCwWFwCckWII3lF5gcWUoN-Os,1265
spacy/tests/lang/sv/test_text.py,sha256=0z-3TIEEmQhbHMxiYGf8oivZSSUwLI1WB3Nbo58s2dk,723
spacy/tests/lang/sv/test_tokenizer.py,sha256=WOSMPHZ4uqKDEKEjhz2xhZEVvVL_-ct8U5aqHB7BRrs,1004
spacy/tests/lang/ta/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ta/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ta/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ta/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/ta/test_text.py,sha256=Wghii5DRyHfMm8Fw9lKCN_YhKm--eLZNtHr3Hnvj1d0,2738
spacy/tests/lang/ta/test_tokenizer.py,sha256=-VDHYj9TwxV-FV_g3AZOVmsAd8Le4oLtwxUbzWcwkuc,7830
spacy/tests/lang/test_attrs.py,sha256=6ZJ5anVXo0UlDBexaKM13h4Xu0c1yVkEmyq-7yehu-w,3532
spacy/tests/lang/test_initialize.py,sha256=yDidzELKuU63ZwOqBtWHqdPBzhftilZmSlC4p5u5CSc,929
spacy/tests/lang/test_lemmatizers.py,sha256=sGHl1d5A5Ne-kQ5on9nZ__K-cwgriV0X2rkff83dH8g,1910
spacy/tests/lang/th/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/th/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/th/__pycache__/test_serialize.cpython-312.pyc,,
spacy/tests/lang/th/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/th/test_serialize.py,sha256=7T_iy2woVxz_IUYBGh9jMDrVZ81RyIHGQAr5D5LOdcE,707
spacy/tests/lang/th/test_tokenizer.py,sha256=mZatsOzEjCR52jtNDxVKg9-xdjWtYcxpCK0rlMxQv6E,318
spacy/tests/lang/ti/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ti/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ti/__pycache__/test_exception.cpython-312.pyc,,
spacy/tests/lang/ti/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ti/test_exception.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ti/test_text.py,sha256=RFpsbPKTrQwkasSw6jK-_sc66l2l_o0XyaQa2OkM4bk,2120
spacy/tests/lang/tl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/tl/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/tl/__pycache__/test_indices.cpython-312.pyc,,
spacy/tests/lang/tl/__pycache__/test_punct.cpython-312.pyc,,
spacy/tests/lang/tl/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/tl/test_indices.py,sha256=YcqYfqjw5tfMru91Tr1EZnCR2gFHMTVQhUP7ZXgJX00,257
spacy/tests/lang/tl/test_punct.py,sha256=yQAIq22Thz2A5VmKkEJG2dhaRvsMrxPWd-ocKN2QNIg,4420
spacy/tests/lang/tl/test_text.py,sha256=UbpIUSzakNwwUsmyn5bguuCAK-w5r9HnvaP8bgMqOsw,2480
spacy/tests/lang/tr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/tr/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/tr/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/tr/__pycache__/test_parser.cpython-312.pyc,,
spacy/tests/lang/tr/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/tr/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/tr/test_noun_chunks.py,sha256=UBYsYytKlOcjcZKMr9dbOPa32mW8IQJWj_Ry30RDXrE,422
spacy/tests/lang/tr/test_parser.py,sha256=hLgGmXM25qdUA7cskg0Km3hpVSEKQnVnNtNNNnWnVYI,19784
spacy/tests/lang/tr/test_text.py,sha256=0_31enZu0U8LFyTrs9d7aObygHS3jCgbdPMYh9t833w,1797
spacy/tests/lang/tr/test_tokenizer.py,sha256=twC9lroAW6QULaL_rI1gqEWsuqIqtnrt3tCoqCgPARM,19588
spacy/tests/lang/tt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/tt/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/tt/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/tt/test_tokenizer.py,sha256=mO-fxTSxE2x_T7yhDXv1TfVtFR3xJln6AZ86amW6-2g,3720
spacy/tests/lang/uk/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/uk/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/uk/__pycache__/test_lemmatizer.cpython-312.pyc,,
spacy/tests/lang/uk/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/uk/__pycache__/test_tokenizer_exc.cpython-312.pyc,,
spacy/tests/lang/uk/test_lemmatizer.py,sha256=1I7A3BxKMsrdGVPWoqTQc2aE17V02n4o5TElMhaQP6s,837
spacy/tests/lang/uk/test_tokenizer.py,sha256=BpRDTXiekbvnud6qE-qKO2CH0AUltU4xLbLdkHryhZc,5415
spacy/tests/lang/uk/test_tokenizer_exc.py,sha256=szWCcQjBPk5G97SCK4SfF7jA9vJu0PaiQlhXrDFEmEg,364
spacy/tests/lang/ur/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ur/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ur/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/ur/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ur/test_prefix_suffix_infix.py,sha256=GeazYeD-BpiH4TsS7fW-tRtM5Oz8oeYzDPcdWuG_Voc,229
spacy/tests/lang/ur/test_text.py,sha256=lArAZ3dKufRA6LWGfZ9Vjixl3uVGD9GKgRM-_3WzpKU,479
spacy/tests/lang/vi/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/vi/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/vi/__pycache__/test_serialize.cpython-312.pyc,,
spacy/tests/lang/vi/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/vi/test_serialize.py,sha256=YbNGUbzDXYgYvgnQoj5rGr-scHmriaGUFkLy5nYDDsE,1309
spacy/tests/lang/vi/test_tokenizer.py,sha256=yBwxjXMtv4NEa1ZgEIw19f-slD7ruMoQfZoiWNOvsqo,1677
spacy/tests/lang/xx/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/xx/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/xx/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/xx/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/xx/test_text.py,sha256=azZego6AkHrcvmDHnf0Bpq2d5L_N4McevniPckQ9Kxw,1720
spacy/tests/lang/xx/test_tokenizer.py,sha256=n3bNfMPPL8Rj30F809suNJNGm-HssU24PkXeE14LRpM,669
spacy/tests/lang/yo/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/yo/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/yo/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/yo/test_text.py,sha256=BxljEi_hVaQ2N6s-foDLhROinwC4HKyNm6qUmzvBrcU,1491
spacy/tests/lang/zh/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/zh/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/zh/__pycache__/test_serialize.cpython-312.pyc,,
spacy/tests/lang/zh/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/zh/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/zh/test_serialize.py,sha256=mn1Xm3-MTikr9pAisj3yYchBrZFVd9MWClvajCRjYfY,1247
spacy/tests/lang/zh/test_text.py,sha256=mm0qcaBicm3NsA7fpPKQ5hAxaefQm8gvf9MeCbxQqIU,454
spacy/tests/lang/zh/test_tokenizer.py,sha256=Bzjrg9MaigmK5BWCnuf6XF1naFxd0WHvpypnkvzLvqM,2819
spacy/tests/matcher/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/matcher/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/matcher/__pycache__/test_dependency_matcher.cpython-312.pyc,,
spacy/tests/matcher/__pycache__/test_levenshtein.cpython-312.pyc,,
spacy/tests/matcher/__pycache__/test_matcher_api.cpython-312.pyc,,
spacy/tests/matcher/__pycache__/test_matcher_logic.cpython-312.pyc,,
spacy/tests/matcher/__pycache__/test_pattern_validation.cpython-312.pyc,,
spacy/tests/matcher/__pycache__/test_phrase_matcher.cpython-312.pyc,,
spacy/tests/matcher/test_dependency_matcher.py,sha256=_LwjiLgL2wRtuHMLmMeAVwO7SWSbjT6JyNT7ZBKv-tI,15046
spacy/tests/matcher/test_levenshtein.py,sha256=VsmtNGHs2kzHZXOgjog2Xeo7tqEiKZ17INJIGRhD4EA,2798
spacy/tests/matcher/test_matcher_api.py,sha256=xN4MTVGH59FHqIJvawjGD-GeJoroITqZFUuL0wxi0TM,29917
spacy/tests/matcher/test_matcher_logic.py,sha256=0A3tI-cBgpKrSMMGX3v8OXcGHyg04jj9bjvW2Uho-Sc,27183
spacy/tests/matcher/test_pattern_validation.py,sha256=9R19Dvea6uCRde9TFKmcEUMryZWLj3w3ChqA12VGbio,3367
spacy/tests/matcher/test_phrase_matcher.py,sha256=J6PNic3GTn02L7koAnJUTpVhwDQzBJGuRfKfgfu7PIg,17967
spacy/tests/morphology/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/morphology/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/morphology/__pycache__/test_morph_converters.cpython-312.pyc,,
spacy/tests/morphology/__pycache__/test_morph_features.cpython-312.pyc,,
spacy/tests/morphology/__pycache__/test_morph_pickle.cpython-312.pyc,,
spacy/tests/morphology/test_morph_converters.py,sha256=KjjcaERN82LEAlB_4XYgIGARgdd7SMj-zJO_TdQOIFc,856
spacy/tests/morphology/test_morph_features.py,sha256=bHXedq8E4bVaED_QDusYmY-RKCfCVcCyOD-pFILWQdI,1349
spacy/tests/morphology/test_morph_pickle.py,sha256=3q7GOTN-cU8CWjS1drkkvxzIhwhKTgLMjuu-2bIKRIQ,670
spacy/tests/package/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/package/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/package/__pycache__/test_requirements.cpython-312.pyc,,
spacy/tests/package/pyproject.toml,sha256=Ecg2ADtTxd_GdPpKDoXGPYyYsi4GUmdTpSADPIoQ3Io,1742
spacy/tests/package/requirements.txt,sha256=dkwWX044ty95Xol8KGeFgEI1k-u0XB7_YhjvYaho66A,897
spacy/tests/package/setup.cfg,sha256=t6smBUaej-weCLXh32Hh7EwUYDRHWakuEJF59rSLDsQ,3861
spacy/tests/package/test_requirements.py,sha256=JXoPfv4ea96iM5x6nzn27Q9QvKTWHMIdcLxu1SpxiHw,3207
spacy/tests/parser/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/parser/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_add_label.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_arc_eager_oracle.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_ner.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_neural_parser.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_nn_beam.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_nonproj.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_parse.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_parse_navigate.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_preset_sbd.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_space_attachment.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_state.cpython-312.pyc,,
spacy/tests/parser/test_add_label.py,sha256=FSiJMXGR8rw1KhgRcHSD7DhhkMKz8-RP7rBMuKMR78s,4920
spacy/tests/parser/test_arc_eager_oracle.py,sha256=IZ5iQ8OBpfurosCZdnhmH1Wd8paDVsjrWz9xBaKCMkI,10086
spacy/tests/parser/test_ner.py,sha256=bpeV1YZvXLGMyqp-thmo3UcD30UQjosbBO-iy-rWgM4,29176
spacy/tests/parser/test_neural_parser.py,sha256=ze57crh16lFrfxSaTI2By4UQO8U3ujkKUqpVZzIMKlk,2859
spacy/tests/parser/test_nn_beam.py,sha256=Ke0YliNJiNTktWf-jqwNV9QApXuUJRVX_6lJ7ZsuZAY,3601
spacy/tests/parser/test_nonproj.py,sha256=oZib0D4ndL4o5FKVxLHTfiKqfOC9hf0V1qMfO4XkpvM,6259
spacy/tests/parser/test_parse.py,sha256=d5iBxq6BlhSvs99NLZGM4793hLYsYGL14EeuDXlNEFI,20158
spacy/tests/parser/test_parse_navigate.py,sha256=PPAOhN7yNe44IJetf_-zqBoau-UMdTBPz2ptjwO-SLY,6216
spacy/tests/parser/test_preset_sbd.py,sha256=L3eMfXgUxl2zXtj200Zx1VNWiCBTd8Kuray8W6NzvaA,2561
spacy/tests/parser/test_space_attachment.py,sha256=NAOFoO4h7_PWppg-8YRJkbjLgM22jOF1vtxEH84G7p8,2928
spacy/tests/parser/test_state.py,sha256=MsbKTmjeS0pM2xGK0dSSTAIttld39z6P7qSn0WWTx_s,1894
spacy/tests/pipeline/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/pipeline/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_analysis.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_annotates_on_update.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_attributeruler.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_edit_tree_lemmatizer.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_entity_linker.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_entity_ruler.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_functions.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_initialize.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_lemmatizer.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_models.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_morphologizer.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_pipe_factories.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_pipe_methods.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_sentencizer.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_senter.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_span_finder.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_span_ruler.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_spancat.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_tagger.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_textcat.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_tok2vec.cpython-312.pyc,,
spacy/tests/pipeline/test_analysis.py,sha256=yadg_YJiqHrsJ-4wkniSNVkr4Iv1bOWGojcxMjVXEG0,3457
spacy/tests/pipeline/test_annotates_on_update.py,sha256=5TmE2LcZHdYAQqUSPvj6_Tbw_-5rNIEpVNpjqolcGmg,3193
spacy/tests/pipeline/test_attributeruler.py,sha256=TuT_WyekcadLdJHjHpgGe38rCjEvbfyeMe_GAPxZxP0,9536
spacy/tests/pipeline/test_edit_tree_lemmatizer.py,sha256=DBH7cuiUZNW0zScV2bZrLn88D1kARzJS4y3POrCktN0,10449
spacy/tests/pipeline/test_entity_linker.py,sha256=zGX2UXEgNMEYWWUNTt-R-ZqgeEMMxoF3S56v09U4j18,48884
spacy/tests/pipeline/test_entity_ruler.py,sha256=l6bkBrxFyzE5CQS2Mw8DXX6EC0Sfy13Qf6XvHMLg00E,25896
spacy/tests/pipeline/test_functions.py,sha256=_ZZd2wLJiyfT95SgQDzShVx7aQPrWIfkWGKEzQcadrQ,3189
spacy/tests/pipeline/test_initialize.py,sha256=R9NI62PBgH9coNKGIWKd5on5xOaScChNOxLMgYes2nY,2437
spacy/tests/pipeline/test_lemmatizer.py,sha256=rFQq41-eeseUcPruD3rgjFRke4AgDTeN2HyUub54zG0,3727
spacy/tests/pipeline/test_models.py,sha256=ocL6WYrJ3pUXW2Ud7mcdYrKRBxXLswYzxWgh-ROYXek,3842
spacy/tests/pipeline/test_morphologizer.py,sha256=mblNq_gkaPYd8e6rEXzsf0P7cqAu1DDNYra2hXh9lBY,8094
spacy/tests/pipeline/test_pipe_factories.py,sha256=TvzMsHk6OnxZMddqd0yOMLj7dLZwf3caH-YXyMbrs3E,19991
spacy/tests/pipeline/test_pipe_methods.py,sha256=zUu4ZHu1Bm8ZeHeeyRANQNO5ywdZ9QKHsGakoAbIbLg,23041
spacy/tests/pipeline/test_sentencizer.py,sha256=tAyT6or6XyI1ISVVULl4hRUnGe0CAvl8ulmyGfI8Mqk,18274
spacy/tests/pipeline/test_senter.py,sha256=c3zBwFwJQN7nFY1PURNM_BEdZC8O7Mh7yV6TTNtBIBU,3313
spacy/tests/pipeline/test_span_finder.py,sha256=Gk5vldJ3Zfk2eOryyqPqQ4pQhdL--ISawIi9bTQauI8,7429
spacy/tests/pipeline/test_span_ruler.py,sha256=4CIC-Z4Sz-XNS_2oOnFiXuumCCe1a_1yJz-hfUnTFgs,16220
spacy/tests/pipeline/test_spancat.py,sha256=gYBM78Im5Lu2nyg8WtfbgKd_jNHoVcuBPiP4tQQd9LA,21976
spacy/tests/pipeline/test_tagger.py,sha256=xCtINavgXF83KeYgzoxvVjz2ZX7Rd_PWzDky8L1-lnw,7653
spacy/tests/pipeline/test_textcat.py,sha256=za030ijOBg640VNvtwzD820npdBtM-MJcn4QLo2b3m4,39730
spacy/tests/pipeline/test_tok2vec.py,sha256=s1-_CIgUNt-ZRr_tWoc8lhCbO9Kl7kknO7WHKZOhDo0,21819
spacy/tests/registry_contents.json,sha256=Aq_si1Wj-Y6EFdoluiLrNWtPb-K383pe7CV_-E-bUiM,6967
spacy/tests/serialize/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/serialize/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_resource_warning.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_config.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_doc.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_docbin.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_extension_attrs.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_kb.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_language.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_pipeline.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_span_groups.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_tokenizer.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_vocab_strings.cpython-312.pyc,,
spacy/tests/serialize/test_resource_warning.py,sha256=L0Jy0atYyMQnYCCG6fpIGnTMFwQWtUnlzLJ0Nc21UHU,4284
spacy/tests/serialize/test_serialize_config.py,sha256=QIOk81QP66__xIbmY-Dqc0z5Gm4aLbO138QO_1O_HZ0,18108
spacy/tests/serialize/test_serialize_doc.py,sha256=Cikrc1rJYl1bwxv-7S1Znj3bXE5USLVoF1wDCU6bfCQ,7370
spacy/tests/serialize/test_serialize_docbin.py,sha256=gYJd69U1daPT2LEpYvOchf4YqWZqFrb-v8DJ582wLZA,4028
spacy/tests/serialize/test_serialize_extension_attrs.py,sha256=vgtbYCu_WeV0vMod0k-_v65EOmB6--AYh-tYryMZALQ,1107
spacy/tests/serialize/test_serialize_kb.py,sha256=FeXfcAWEZVpOpLePPFNn9NNLITHJHq_RAQ-hrns4pAU,7066
spacy/tests/serialize/test_serialize_language.py,sha256=Mmm0hRtxTDpYUtsbZSFLXefC406nCkfOOMnz6OIZKQU,3594
spacy/tests/serialize/test_serialize_pipeline.py,sha256=NXFD7c0laV83rGvYbbPeqqxGHRtz625ekBOqw-eiZy4,16840
spacy/tests/serialize/test_serialize_span_groups.py,sha256=UxGTuI9_KavoLU99TjPCA-YyQquM0h3RglxARcvVTjY,8768
spacy/tests/serialize/test_serialize_tokenizer.py,sha256=tPytX8AFMZ54ZXSbBXgTULGdTkHhLcQoL12vyMElBTs,5445
spacy/tests/serialize/test_serialize_vocab_strings.py,sha256=7-elx0KAzXph6ChPkpEIkKXPoLihluFkYEwG_aGAXaQ,7067
spacy/tests/test_architectures.py,sha256=g8GK9w41Gg6juR_cqfQwWb_tXalmFIR3i1g0NBLwo5U,448
spacy/tests/test_cli.py,sha256=wadSY_Rylv5xj7vj8Wz_W2aD4X4DCuO_VK_nBnyjnDg,40890
spacy/tests/test_cli_app.py,sha256=y-qxnSzMnggVW1AN3jUacqi9VXmmkxsjribambJ9yPU,13985
spacy/tests/test_displacy.py,sha256=p8PeIcJbGwSzQeWbVL4Qb9nCL30ECJqjoci0gXdViPY,17774
spacy/tests/test_errors.py,sha256=xsQ5X2M48nrx7CYwpgSZfG_Vw13lyyFpM4fkUoSofOI,333
spacy/tests/test_factory_imports.py,sha256=Jm1F7P5MTBWkfoRCzaIMuuunYEpWjV-YLVoXJA8rCZ4,3861
spacy/tests/test_factory_registrations.py,sha256=c2wV1jZYzQ1R_sPbv5P5nkRx1z2rAU4O6kBRC9EsJWI,3008
spacy/tests/test_language.py,sha256=WX6-ST-D9gc6f5JJ3qQzqAWkICP9ZV7aS_yZw1I14wc,27194
spacy/tests/test_misc.py,sha256=_G_PsMW_R7Ia6wynwjGfHiEyKqsBPLLFXeOhz9Uq0_w,15497
spacy/tests/test_models.py,sha256=AQfsM2V-Jy57Hp6DcTTqhYyO7MDcBUiPcf119ttTG5g,9591
spacy/tests/test_pickles.py,sha256=TsiMcr6CfjAm7KGc0CgViD5wBuJcWH8MBX2wgw9K9j8,2023
spacy/tests/test_registry_population.py,sha256=f90OkCK-2nj3JoK0ObywgKpgHzVOXOCR8G8yyetl6dw,1910
spacy/tests/test_scorer.py,sha256=p8uMK4f3lV0MDNkMUHY83IIeWLTIgOPFZ_yrmUDZSF0,17478
spacy/tests/test_ty.py,sha256=PDz_quaqJzvd2fTWvJPS2J1iurOe3WFMdSIPDdI-MM0,748
spacy/tests/tok2vec.py,sha256=375MQp1umNocoL0nAmFnyXHem05kPisR1tXZW8PdpmI,1001
spacy/tests/tokenizer/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/tokenizer/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/tokenizer/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/tokenizer/__pycache__/test_explain.cpython-312.pyc,,
spacy/tests/tokenizer/__pycache__/test_naughty_strings.cpython-312.pyc,,
spacy/tests/tokenizer/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/tokenizer/__pycache__/test_urls.cpython-312.pyc,,
spacy/tests/tokenizer/__pycache__/test_whitespace.cpython-312.pyc,,
spacy/tests/tokenizer/sun.txt,sha256=U0cMmOTdYomgLa9Uazq5T4ZMA682Kg4w9NtSA8zAsm8,2591
spacy/tests/tokenizer/test_exceptions.py,sha256=iwmYbnfZAdsYVktA7GH3Yh4wdSVf789gW4EJj437HFo,1757
spacy/tests/tokenizer/test_explain.py,sha256=P-FX1rgfhz8gAWUqe-jWCZ4epPqIZTlu2I9SxEGeSGQ,5441
spacy/tests/tokenizer/test_naughty_strings.py,sha256=3j3YIw8IG7dL3RtmwkSbCoeIpwbzWZ2-NLNAo5IlLlo,7466
spacy/tests/tokenizer/test_tokenizer.py,sha256=-nHbANp-Jx-guc7V9ATIp-hqGp8_QikjqDxzPeqMMBs,18588
spacy/tests/tokenizer/test_urls.py,sha256=a3sUJRLGAOROCSc5Cjr-f5cv9AS06bMd5n4yGlWLauk,6462
spacy/tests/tokenizer/test_whitespace.py,sha256=7NNlyfg8vBiP3bQdQK4-QywaFXIRmDIkzZesBxeiwKg,1295
spacy/tests/training/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/training/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_augmenters.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_corpus.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_logger.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_new_example.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_pretraining.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_readers.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_rehearse.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_training.cpython-312.pyc,,
spacy/tests/training/test_augmenters.py,sha256=dRcb7_U2cO4H4i6lktciiu_HZUx00d8BvuShYdPehdA,10558
spacy/tests/training/test_corpus.py,sha256=qt463IuIiPvN9kp4V2qBAU3HxoQMJt6VSmMoxz5z3Hw,1942
spacy/tests/training/test_logger.py,sha256=N8-03h03_G6XKFUNSfLGcV3Dd77NXZDTsZnToqmoneY,600
spacy/tests/training/test_new_example.py,sha256=qfhwhmvju9N6HsqRY8HxQBx8tqUybE-Teidq1JbUXOY,16146
spacy/tests/training/test_pretraining.py,sha256=FWkieCZfOvF2kMXZ60oRFb2Q14aPYu9eFnYGAVS87mQ,12064
spacy/tests/training/test_readers.py,sha256=r6VWo0mp4dJJLQnIL7D3HfkKDCcX4WJi_YE0ciL5BM8,3949
spacy/tests/training/test_rehearse.py,sha256=VL4eVw4_NmFFZhsf4QaRX0Ng3eXSgX9oMjzqvxKX2Ks,6405
spacy/tests/training/test_training.py,sha256=QJztDd08m5KOwCdCXgtYLRbMoA7E_7QruvJ4ctYvmbI,45549
spacy/tests/util.py,sha256=ZKhTkLh88PQYmEihQ4iis7kxILnJbKTCDfYgKIuKlBY,3281
spacy/tests/vocab_vectors/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/vocab_vectors/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_lexeme.cpython-312.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_lookups.cpython-312.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_memory_zone.cpython-312.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_similarity.cpython-312.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_stringstore.cpython-312.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_vectors.cpython-312.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_vocab_api.cpython-312.pyc,,
spacy/tests/vocab_vectors/test_lexeme.py,sha256=58PN-I1iwvaaklbrcqYN2-obyEgzggR0GrYQfluVUwo,2853
spacy/tests/vocab_vectors/test_lookups.py,sha256=l9z9TAIPVsJHgcwZr1HtLCka2I68SrpfgxEC7nhq_TU,4652
spacy/tests/vocab_vectors/test_memory_zone.py,sha256=kymj7vT62_nKHPAGlF_pNZFtOLtSs_xVHPZ9vQfhL54,905
spacy/tests/vocab_vectors/test_similarity.py,sha256=HYjlLR7DpPjv7cum7ZJ5bdTDT5I3ZexeOoT8qzJjiUY,3835
spacy/tests/vocab_vectors/test_stringstore.py,sha256=W7MdimzJC79cIQF9mKj2yVoEVgwRDGqAlej6ILiTiIk,3339
spacy/tests/vocab_vectors/test_vectors.py,sha256=OlYEPqcyuMnPoDgBkjaU8R3sQ50p82_plLKlezc4aAI,23814
spacy/tests/vocab_vectors/test_vocab_api.py,sha256=tkHO55TnfX2IMqK5lFAS2z2LOEL6eofPEGNZmhbXL4s,2148
spacy/tokenizer.cpython-312-darwin.so,sha256=fgDBG9p8UUAbOIQ84Kx7TEsorsrAmiqkggedpv7mAOo,408896
spacy/tokenizer.pxd,sha256=Cid5LNQ1yUAKKTbMRD0aOAcqv2RTEz0-fCsh-shF0_8,2218
spacy/tokenizer.pyx,sha256=0dUCw-8KaklVKXUe9oi5YBxH5amSxmr_kgOfe2_w9U8,36775
spacy/tokens/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tokens/__init__.py,sha256=OTJhFmiCsNQRvRcAwpYs34x6UicZ1sIpg0fE8GYTboI,251
spacy/tokens/__pycache__/__init__.cpython-312.pyc,,
spacy/tokens/__pycache__/_dict_proxies.cpython-312.pyc,,
spacy/tokens/__pycache__/_serialize.cpython-312.pyc,,
spacy/tokens/__pycache__/underscore.cpython-312.pyc,,
spacy/tokens/_dict_proxies.py,sha256=c25woP5ZWTh0n_TYx1lfrms2FT2xzBKFJEBz-It2yuk,4515
spacy/tokens/_retokenize.cpython-312-darwin.so,sha256=CUWPwVNsnAaSl6k0bMZA6SOffQretRdGOKvagh-IxE0,329648
spacy/tokens/_retokenize.pyi,sha256=oxX2MRE7S9UtnO4Qi2Ki0kWJOYrR6XKDk-HNujLxZzE,703
spacy/tokens/_retokenize.pyx,sha256=Dln3peZj9xDOm3XROkz9qGybiCT_55bRSGvDb1wdGgE,20411
spacy/tokens/_serialize.py,sha256=FVhNJ2ba96zAGzUk9AKJsa3xQP-RNNX5DnV-IYZYTtM,11991
spacy/tokens/doc.cpython-312-darwin.so,sha256=0X98grHDm9TPxRnHEl8_DEsoMXyIjY7BqAKoAJ6FOFU,724872
spacy/tokens/doc.pxd,sha256=wOz-xHlCsc4doLpJnlNM7UJc2Kj98SHW1LHRBSkhAcc,1684
spacy/tokens/doc.pyi,sha256=lBeNu9w36qHfCdf8pW5zVvY7wQnWBR_S9SsAmmAG5GM,6163
spacy/tokens/doc.pyx,sha256=xPHqPwsfeq-eyKb4d3HrEXE7VmQWpxUu-NGNLVBpfmc,83338
spacy/tokens/graph.cpython-312-darwin.so,sha256=YnC9eZpxbCkZegTKUTenhHvQqj-h-zHOTzxby4ZfXjo,291496
spacy/tokens/graph.pxd,sha256=expdzSuiUlyzv6LUmgQkLub3bbSWYGASV3jUjMyszmA,296
spacy/tokens/graph.pyx,sha256=FQLFTUzSzcy3Mi-1MD8fdb24WkAIo05v3EL80tqDVho,22906
spacy/tokens/morphanalysis.cpython-312-darwin.so,sha256=9zjkLZ5ikknFw3SSlNqMez4rNwGcr7ucNg-aSiv_5KE,136784
spacy/tokens/morphanalysis.pxd,sha256=lfnnTk2XRJPrdKmSzYKZFKcUryaeOOc73hNjo8BCFO8,209
spacy/tokens/morphanalysis.pyi,sha256=s3PMXIoYSU8uh6xNtYiGDMyHfR6AC7GBkBsuC8pOZNg,861
spacy/tokens/morphanalysis.pyx,sha256=lFdt5izAn3f4wTm5byKFvZzPqGNBx65E5Ncq7ELFKrA,3016
spacy/tokens/span.cpython-312-darwin.so,sha256=5KzPMGaoBCcdtO3JlMfMEBxQfl8oypFiqQOezH_J5bo,384616
spacy/tokens/span.pxd,sha256=ecGg6PH3751AjWjyRIJSDL4jucD8E6-V5xz025kCxSM,521
spacy/tokens/span.pyi,sha256=IYtXAEDpW4l2v5_e340S8vdMprUDwIj9eKqT9QfHe5Q,3952
spacy/tokens/span.pyx,sha256=UTqdltKZ0kba0E_nk9apWQunQ1Faf3zbSgv6FchXCfg,33549
spacy/tokens/span_group.cpython-312-darwin.so,sha256=B_EG4l1P6B35AsCHMHcrplOBc5JmfFcwDXqqyNU0Ca8,288416
spacy/tokens/span_group.pxd,sha256=JkgydKJRnxMA076G4WHId5bLmg3xwz6oSQQWzp8Rd5Q,246
spacy/tokens/span_group.pyi,sha256=xkxR0RKu-DEL5M9QxQHDS3LKLNtI8vHZcb5LC8X-Xxk,864
spacy/tokens/span_group.pyx,sha256=PGV7W867lBS8-PSH_tfgw90FHz4ZuXEMrtkiwIhMU2s,11679
spacy/tokens/token.cpython-312-darwin.so,sha256=I4UfNGmr4YB8z2BUhD1PnSb-t6bpnt_6ijrGVyzBnpc,342216
spacy/tokens/token.pxd,sha256=7kxw1Zsy1m3GVhihyNeP0g4az8xFOfbOkS6gs60ZNTw,3456
spacy/tokens/token.pyi,sha256=U_7he7fq9_mOpzu1HhQyVTyUKo6bcG095aQmLYAxGxQ,5581
spacy/tokens/token.pyx,sha256=XChy7XzOPp6TNwAjTsen4vHz3gyDfi7mWgtCxYeBFVo,33403
spacy/tokens/underscore.py,sha256=5_uEOZhfkSCcs9sheFJsgsWuU5LheopRQYAjVFjV63g,5590
spacy/training/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/training/__init__.py,sha256=TSC6If5fYqxc9Ob1cgtlsy50IAgUeiovIXMuZkCZSRY,1302
spacy/training/__pycache__/__init__.cpython-312.pyc,,
spacy/training/__pycache__/alignment.cpython-312.pyc,,
spacy/training/__pycache__/augment.cpython-312.pyc,,
spacy/training/__pycache__/batchers.cpython-312.pyc,,
spacy/training/__pycache__/callbacks.cpython-312.pyc,,
spacy/training/__pycache__/corpus.cpython-312.pyc,,
spacy/training/__pycache__/initialize.cpython-312.pyc,,
spacy/training/__pycache__/iob_utils.cpython-312.pyc,,
spacy/training/__pycache__/loggers.cpython-312.pyc,,
spacy/training/__pycache__/loop.cpython-312.pyc,,
spacy/training/__pycache__/pretrain.cpython-312.pyc,,
spacy/training/align.cpython-312-darwin.so,sha256=t-V1lEXMQR6jr5e4NXXFx1oikeJsQGjSoRi-avr_pXc,128664
spacy/training/align.pyx,sha256=sdS44mf2s7yX5lZyKJuNdkDmrfgJ9w2CImk3xuZ9SUI,3290
spacy/training/alignment.py,sha256=Vad78-MDeYrgpAGqe-KWPnw2wI3PlIMl7YI1nPOYapU,614
spacy/training/alignment_array.cpython-312-darwin.so,sha256=ULcisJ1QmcaIuQpFm3nk6dSnt721gdhB9Bp2ZH-A4Rg,110344
spacy/training/alignment_array.pxd,sha256=G7bSSXoXHe3SDfbCYGGeoJsoN4bMkI-keqWLSAWHkSM,171
spacy/training/alignment_array.pyx,sha256=yke2w86g6zlIDwsx081TzzwlwpelHvOHRWmoCd3Eck4,2155
spacy/training/augment.py,sha256=CZY0eP_4isVSQsFwVK1g8IaKpol9uqC5LtugTbsQCfM,13118
spacy/training/batchers.py,sha256=jEPCvbwVmgYCda43gcHR7zoxuAsqgJBDtRZFFaJcsM4,8990
spacy/training/callbacks.py,sha256=9UdUKsxUGuzs1jaOg8hh7bjyxZzq_sBRXcPHBC88r-A,1259
spacy/training/converters/__init__.py,sha256=Mxhb1pMbVmF736qOAnrNoNXtwu3nC6Bmf0tfcE4TS0Y,224
spacy/training/converters/__pycache__/__init__.cpython-312.pyc,,
spacy/training/converters/__pycache__/conll_ner_to_docs.cpython-312.pyc,,
spacy/training/converters/__pycache__/conllu_to_docs.cpython-312.pyc,,
spacy/training/converters/__pycache__/iob_to_docs.cpython-312.pyc,,
spacy/training/converters/__pycache__/json_to_docs.cpython-312.pyc,,
spacy/training/converters/conll_ner_to_docs.py,sha256=jZo6_aU3-klh9M_y8Bgx-Vyh2rqZFyTmgWcGUK6I2qA,6177
spacy/training/converters/conllu_to_docs.py,sha256=d0puumbPJ-wwURJNk1uDfDUD3pTQL5ZnhAPMyNTnavU,10276
spacy/training/converters/iob_to_docs.py,sha256=Vi1R9fQimV1m4ZL792WPtYsi1p3ok0NEEV056jFC6pg,2356
spacy/training/converters/json_to_docs.py,sha256=laBlc84bxT2KV1TE072l_nw8vcxSYbHEI-1Jnkhu8G4,880
spacy/training/corpus.py,sha256=I9fB3sh0V5FQDBuSVVRJ8GbF4PfbBcFpTe18rcWCEmk,11978
spacy/training/example.cpython-312-darwin.so,sha256=gM_LAUJASJS3KCMRSly94hn8BndMvgfeAwLS2z3fi40,438480
spacy/training/example.pxd,sha256=_aThcOQ5S2lOBEG6CwipWUU88Tk7gcn5FK96zrrhd-k,328
spacy/training/example.pyi,sha256=hW57UL12v-wwNfMLkB6vD2ia6LAlU2YRQbEChaEk498,2016
spacy/training/example.pyx,sha256=vYrjVD4KeUs-KOyUtqCGMT1ymPLitxhNlrX6BElESvk,24805
spacy/training/gold_io.cpython-312-darwin.so,sha256=9Qp5lwYFUralnxuJ9NmgGi1KSoONd08JgyP1cGL9ygc,173392
spacy/training/gold_io.pyx,sha256=yEZ_krPgl90Ugh-qK0azBni17_FgvdNkAt8ciqMDnOM,8240
spacy/training/initialize.py,sha256=JbxLenOGH-dUNut9VUJwkHgMzTdBulfzwDQYuGPZMXc,14144
spacy/training/iob_utils.py,sha256=GlYn__gKZjw0A8JkSPNIgY6P2xrgsG_Q7jPIdzlKRhE,9075
spacy/training/loggers.py,sha256=vXkV01ZIauQALk-H-7XChtBNvosXadgPOH9e3vzg-Vc,7732
spacy/training/loop.py,sha256=SKuj4XH9nY8duMkS9X2PHVhYF5bz7MzcBDAKp1ZLAIk,15029
spacy/training/pretrain.py,sha256=LS6JJg5X5Di-WcjBNqgcc6II-iEfs4uSYiMk8NVW_7w,9710
spacy/ty.py,sha256=yb_6s-iWG5jzhWfTxD3bVwFXB2InTcSnJFRCkMxXv2g,1345
spacy/typedefs.pxd,sha256=9hhLqcJTMZ6mTPbcS6ToW3sk-vNZanPPMNm9KWSIG8w,258
spacy/typedefs.pyx,sha256=vgsVAWry2IrwgVJQDjxVtJCvfNfbtmCcLguNYC73weU,24
spacy/util.py,sha256=Oo_wSMkOAICyoR-FhlOpnU7DcguHQAAHc1jQmrvYdgQ,67860
spacy/vectors.cpython-312-darwin.so,sha256=FfNh14zIm_jDF2CaMEmajzIqN58jR3Qv2rzJxbMSGLo,368704
spacy/vectors.pyx,sha256=2fDojdKuPyrR49B3vXfhXMCHmtE95JNcqW7sN8HoHck,28248
spacy/vocab.cpython-312-darwin.so,sha256=UeCVr02MJD7llZK1ZhYshd3o3nDUB0FUccALGCYTyLQ,387608
spacy/vocab.pxd,sha256=HCAezk-hAgXldbxOwj_qqVzcDDI1U2uW64aLbtDfYYI,1463
spacy/vocab.pyi,sha256=mgJoXUwK1Z1P6XGBZ8Kq_mAPsTij9tO49wa_ZiopQ5o,2900
spacy/vocab.pyx,sha256=RHx7o6y7u0MO4AahY2yEQJGkPtQNon5P5MkjrR1oZtA,26069
