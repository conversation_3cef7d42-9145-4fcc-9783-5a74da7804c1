from thinc.api import <PERSON><PERSON>, <PERSON><PERSON>, add, chain, reduce_max

good_model = chain(<PERSON><PERSON>(10), <PERSON><PERSON>(10), <PERSON>max())
reveal_type(good_model)

good_model2 = add(<PERSON><PERSON>(10), <PERSON><PERSON>(10), <PERSON>max())
reveal_type(good_model2)

bad_model_undetected = chain(<PERSON><PERSON>(10), <PERSON><PERSON>(10), reduce_max(), Softmax())
reveal_type(bad_model_undetected)

bad_model_undetected2 = add(<PERSON><PERSON>(10), <PERSON><PERSON>(10), reduce_max(), Softmax())
reveal_type(bad_model_undetected2)
