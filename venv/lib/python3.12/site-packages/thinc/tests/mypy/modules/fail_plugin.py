from thinc.api import <PERSON><PERSON>, <PERSON><PERSON>, add, chain, concatenate, reduce_max

bad_model = chain(<PERSON><PERSON>(10), reduce_max(), Softmax())

bad_model2 = add(<PERSON><PERSON>(10), reduce_max(), Softmax())

bad_model_only_plugin = chain(
    <PERSON><PERSON>(10), <PERSON><PERSON>(10), <PERSON><PERSON>(10), <PERSON><PERSON>(10), reduce_max(), Softmax()
)

bad_model_only_plugin2 = add(
    <PERSON><PERSON>(10), <PERSON><PERSON>(10), <PERSON><PERSON>(10), <PERSON><PERSON>(10), reduce_max(), Softmax()
)
reveal_type(bad_model_only_plugin2)

bad_model_only_plugin3 = concatenate(
    <PERSON><PERSON>(10), <PERSON><PERSON>(10), <PERSON><PERSON>(10), <PERSON><PERSON>(10), reduce_max(), Softmax()
)

reveal_type(bad_model_only_plugin3)
