3: error: Cannot infer type argument 2 of "chain"  [misc]
3: error: Layer outputs type (thinc.types.Floats2d) but the next layer expects (thinc.types.Ragged) as an input  [layer-mismatch-output]
3: error: Layer input type (thinc.types.Ragged) is not compatible with output (thinc.types.Floats2d) from previous layer  [layer-mismatch-input]
5: error: Cannot infer type argument 1 of "add"  [misc]
5: error: Layer input (thinc.types.Floats2d) not compatible with next layer input (thinc.types.Ragged)  [layer-mismatch-input]
5: error: Layer input (thinc.types.Ragged) not compatible with previous layer input (thinc.types.Floats2d)  [layer-mismatch-input]
5: error: Layer input (thinc.types.Ragged) not compatible with next layer input (thinc.types.Floats2d)  [layer-mismatch-input]
5: error: Layer input (thinc.types.Floats2d) not compatible with previous layer input (thinc.types.Ragged)  [layer-mismatch-input]
8: error: Layer outputs type (thinc.types.Floats2d) but the next layer expects (thinc.types.Ragged) as an input  [layer-mismatch-output]
8: error: Layer input type (thinc.types.Ragged) is not compatible with output (thinc.types.Floats2d) from previous layer  [layer-mismatch-input]
12: error: Layer input (thinc.types.Floats2d) not compatible with next layer input (thinc.types.Ragged)  [layer-mismatch-input]
12: error: Layer input (thinc.types.Ragged) not compatible with previous layer input (thinc.types.Floats2d)  [layer-mismatch-input]
12: error: Layer input (thinc.types.Ragged) not compatible with next layer input (thinc.types.Floats2d)  [layer-mismatch-input]
12: error: Layer input (thinc.types.Floats2d) not compatible with previous layer input (thinc.types.Ragged)  [layer-mismatch-input]
14: note: Revealed type is "thinc.model.Model[thinc.types.Floats2d, thinc.types.Floats2d]"
17: error: Layer input (thinc.types.Floats2d) not compatible with next layer input (thinc.types.Ragged)  [layer-mismatch-input]
17: error: Layer input (thinc.types.Ragged) not compatible with previous layer input (thinc.types.Floats2d)  [layer-mismatch-input]
17: error: Layer input (thinc.types.Ragged) not compatible with next layer input (thinc.types.Floats2d)  [layer-mismatch-input]
17: error: Layer input (thinc.types.Floats2d) not compatible with previous layer input (thinc.types.Ragged)  [layer-mismatch-input]
20: note: Revealed type is "thinc.model.Model[thinc.types.Floats2d, thinc.types.Floats2d]"
