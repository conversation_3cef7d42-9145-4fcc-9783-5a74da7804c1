"""
Database management utilities for the IPO Tracker application.
This handles database connections, initialization, and common operations.
"""

from contextlib import contextmanager
from typing import Dict, Generator, Optional
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session, sessionmaker
from loguru import logger
from models import create_database_engine, create_tables, get_session_maker, DataSource
from config import Config

class DatabaseManager:
    """Manages database connections and operations"""

    def __init__(self) -> None:
        self.engine: Optional[Engine] = None
        self.SessionMaker: Optional[sessionmaker[Session]] = None
        self._initialize()

    def _initialize(self) -> None:
        """Initialize database connection"""
        try:
            self.engine = create_database_engine()
            self.SessionMaker = get_session_maker(self.engine)
            logger.info("Database connection initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def create_all_tables(self) -> None:
        """Create all database tables"""
        try:
            if self.engine is None:
                raise RuntimeError("Database engine not initialized")
            create_tables(self.engine)
            logger.info("Database tables created successfully")
            self._initialize_data_sources()
        except Exception as e:
            logger.error(f"Failed to create tables: {e}")
            raise
    
    def _initialize_data_sources(self) -> None:
        """Initialize data source tracking records"""
        with self.get_session() as session:
            try:
                # Check if data sources already exist
                existing_sources = session.query(DataSource).count()
                if existing_sources > 0:
                    logger.info("Data sources already initialized")
                    return
                
                # Create initial data source records
                sources = [
                    DataSource(source_name="SEC_EDGAR", is_active=True),
                    DataSource(source_name="ALPHA_VANTAGE", is_active=True),
                    DataSource(source_name="YAHOO_FINANCE", is_active=True),
                ]
                
                for source in sources:
                    session.add(source)
                
                session.commit()
                logger.info("Data sources initialized successfully")
                
            except Exception as e:
                session.rollback()
                logger.error(f"Failed to initialize data sources: {e}")
                raise
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """Get a database session with automatic cleanup"""
        if self.SessionMaker is None:
            raise RuntimeError("Database not initialized")
        session = self.SessionMaker()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            with self.get_session() as session:
                # Simple query to test connection
                from sqlalchemy import text
                session.execute(text("SELECT 1")).fetchone()
                logger.info("Database connection test successful")
                return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    def get_stats(self) -> Dict[str, int]:
        """Get database statistics"""
        try:
            with self.get_session() as session:
                from models import Company, IPO, AIReport
                
                stats = {
                    'companies': session.query(Company).count(),
                    'ipos': session.query(IPO).count(),
                    'ai_reports': session.query(AIReport).count(),
                }
                
                return stats
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            return {}

# Global database manager instance
db_manager = DatabaseManager()

def init_database() -> None:
    """Initialize the database (create tables, etc.)"""
    logger.info("Initializing database...")
    
    # Test connection first
    if not db_manager.test_connection():
        raise Exception("Cannot connect to database. Please check your DATABASE_URL in .env file")
    
    # Create tables
    db_manager.create_all_tables()
    
    logger.info("Database initialization complete")

def get_db_session():
    """Get a database session (convenience function)"""
    return db_manager.get_session()

if __name__ == "__main__":
    # This allows you to run: python database.py to initialize the database
    try:
        Config.validate()
        init_database()
        
        # Show stats
        stats = db_manager.get_stats()
        print(f"Database initialized successfully!")
        print(f"Current stats: {stats}")
        
    except Exception as e:
        print(f"Database initialization failed: {e}")
        print("\nMake sure you have:")
        print("1. PostgreSQL installed and running")
        print("2. Created a database named 'ipo_tracker'")
        print("3. Set up your .env file with correct DATABASE_URL")
