"""
Setup script for the IPO Tracker application.
This helps you get everything installed and configured.
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a shell command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def check_postgresql():
    """Check if PostgreSQL is installed and running"""
    print("\n🔍 Checking PostgreSQL...")
    
    # Check if psql command exists
    try:
        subprocess.run(["psql", "--version"], check=True, capture_output=True)
        print("✅ PostgreSQL client found")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ PostgreSQL not found. Please install PostgreSQL first.")
        print("   macOS: brew install postgresql")
        print("   Ubuntu: sudo apt-get install postgresql postgresql-contrib")
        print("   Windows: Download from https://www.postgresql.org/download/")
        return False
    
    return True

def create_virtual_environment():
    """Create a Python virtual environment"""
    venv_path = Path("venv")
    if venv_path.exists():
        print("✅ Virtual environment already exists")
        return True
    
    return run_command("python -m venv venv", "Creating virtual environment")

def install_dependencies():
    """Install Python dependencies"""
    # Determine the correct pip command based on OS
    if sys.platform == "win32":
        pip_cmd = "venv\\Scripts\\pip"
    else:
        pip_cmd = "venv/bin/pip"
    
    return run_command(f"{pip_cmd} install -r requirements.txt", "Installing Python dependencies")

def setup_environment_file():
    """Set up the .env file"""
    env_file = Path(".env")
    example_file = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if example_file.exists():
        # Copy example to .env
        with open(example_file, 'r') as f:
            content = f.read()
        
        with open(env_file, 'w') as f:
            f.write(content)
        
        print("✅ Created .env file from example")
        print("⚠️  Please edit .env file with your actual database credentials and API keys")
        return True
    else:
        print("❌ .env.example file not found")
        return False

def create_database():
    """Create the PostgreSQL database"""
    print("\n🔄 Setting up PostgreSQL database...")
    print("Please enter your PostgreSQL credentials:")
    
    # Get database credentials
    db_user = input("PostgreSQL username (default: postgres): ").strip() or "postgres"
    db_password = input("PostgreSQL password: ").strip()
    db_host = input("Database host (default: localhost): ").strip() or "localhost"
    db_port = input("Database port (default: 5432): ").strip() or "5432"
    db_name = input("Database name (default: ipo_tracker): ").strip() or "ipo_tracker"
    
    # Create database
    create_db_cmd = f'psql -h {db_host} -p {db_port} -U {db_user} -c "CREATE DATABASE {db_name};"'
    
    print(f"\n🔄 Creating database '{db_name}'...")
    try:
        subprocess.run(create_db_cmd, shell=True, check=True, input=db_password, text=True)
        print(f"✅ Database '{db_name}' created successfully")
    except subprocess.CalledProcessError:
        print(f"⚠️  Database '{db_name}' might already exist or creation failed")
        print("   This is okay if the database already exists")
    
    # Update .env file with database URL
    database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r') as f:
            content = f.read()
        
        # Replace the DATABASE_URL line
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('DATABASE_URL='):
                lines[i] = f"DATABASE_URL={database_url}"
                break
        
        with open(env_file, 'w') as f:
            f.write('\n'.join(lines))
        
        print("✅ Updated .env file with database URL")
    
    return True

def main():
    """Main setup function"""
    print("🚀 IPO Tracker Setup")
    print("=" * 50)
    
    # Check prerequisites
    if not check_python_version():
        return False
    
    if not check_postgresql():
        return False
    
    # Setup steps
    steps = [
        (create_virtual_environment, "Virtual environment setup"),
        (install_dependencies, "Dependency installation"),
        (setup_environment_file, "Environment file setup"),
        (create_database, "Database setup"),
    ]
    
    for step_func, step_name in steps:
        if not step_func():
            print(f"\n❌ Setup failed at: {step_name}")
            return False
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit the .env file with your API keys:")
    print("   - Get OpenAI API key from: https://platform.openai.com/api-keys")
    print("   - Get Alpha Vantage key from: https://www.alphavantage.co/support/#api-key")
    print("2. Initialize the database: python database.py")
    print("3. Start the application: python main.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
