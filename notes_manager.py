"""
IPO Notes Management Utility
This script helps you add, view, and manage notes for IPOs in your database.
"""

from datetime import datetime
from typing import Optional
from sqlalchemy.orm import joinedload
from database import get_db_session
from models import Company, IPO
from loguru import logger

class NotesManager:
    """Manages notes for IPO entries"""

    def add_note(self, company_name_or_ticker: str, note_text: str) -> bool:
        """Add a note to an IPO by company name or ticker"""
        with get_db_session() as session:
            try:
                # Find the company and its IPO
                company = session.query(Company).filter(
                    (Company.name.ilike(f"%{company_name_or_ticker}%")) |
                    (Company.ticker_symbol.ilike(f"%{company_name_or_ticker}%"))
                ).first()
                
                if not company:
                    print(f"❌ Company '{company_name_or_ticker}' not found")
                    return False
                
                # Get the most recent IPO for this company
                ipo = session.query(IPO).filter(
                    IPO.company_id == company.id
                ).order_by(IPO.created_at.desc()).first()
                
                if not ipo:
                    print(f"❌ No IPO found for company '{company.name}'")
                    return False
                
                # Add the note
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")
                current_notes = getattr(ipo, 'notes', None)
                if current_notes:
                    # Append to existing notes with timestamp
                    setattr(ipo, 'notes', current_notes + f"\n\n[{timestamp}] {note_text}")
                else:
                    # Create new note with timestamp
                    setattr(ipo, 'notes', f"[{timestamp}] {note_text}")
                
                session.commit()
                print(f"✅ Note added to {company.name} ({company.ticker_symbol or 'No ticker'})")
                return True
                
            except Exception as e:
                session.rollback()
                logger.error(f"Failed to add note: {e}")
                print(f"❌ Failed to add note: {e}")
                return False
    
    def view_notes(self, company_name_or_ticker: Optional[str] = None) -> None:
        """View notes for a specific company or all companies with notes"""
        with get_db_session() as session:
            try:
                query = session.query(IPO).options(joinedload(IPO.company)).filter(
                    IPO.notes.isnot(None),
                    IPO.notes != ""
                )
                
                if company_name_or_ticker:
                    # Filter by specific company
                    query = query.join(Company).filter(
                        (Company.name.ilike(f"%{company_name_or_ticker}%")) |
                        (Company.ticker_symbol.ilike(f"%{company_name_or_ticker}%"))
                    )
                
                ipos_with_notes = query.all()
                
                if not ipos_with_notes:
                    if company_name_or_ticker:
                        print(f"❌ No notes found for '{company_name_or_ticker}'")
                    else:
                        print("❌ No IPOs with notes found")
                    return
                
                print("\n📝 IPO NOTES")
                print("=" * 60)
                
                for ipo in ipos_with_notes:
                    company = ipo.company
                    print(f"\n🏢 {company.name}")
                    if company.ticker_symbol:
                        print(f"   Ticker: {company.ticker_symbol}")
                    print(f"   IPO Status: {ipo.status}")
                    expected_date = getattr(ipo, 'expected_date', None)
                    if expected_date:
                        print(f"   Expected Date: {expected_date.strftime('%Y-%m-%d')}")
                    print(f"\n   Notes:")
                    # Indent the notes for better readability
                    notes_text = getattr(ipo, 'notes', '') or ''
                    note_lines = notes_text.split('\n')
                    for line in note_lines:
                        print(f"   {line}")
                    print("-" * 60)
                
            except Exception as e:
                logger.error(f"Failed to view notes: {e}")
                print(f"❌ Failed to view notes: {e}")
    
    def update_note(self, company_name_or_ticker: str, new_note_text: str) -> bool:
        """Replace all notes for an IPO with new text"""
        with get_db_session() as session:
            try:
                # Find the company and its IPO
                company = session.query(Company).filter(
                    (Company.name.ilike(f"%{company_name_or_ticker}%")) |
                    (Company.ticker_symbol.ilike(f"%{company_name_or_ticker}%"))
                ).first()
                
                if not company:
                    print(f"❌ Company '{company_name_or_ticker}' not found")
                    return False
                
                ipo = session.query(IPO).filter(
                    IPO.company_id == company.id
                ).order_by(IPO.created_at.desc()).first()
                
                if not ipo:
                    print(f"❌ No IPO found for company '{company.name}'")
                    return False
                
                # Update the note
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")
                setattr(ipo, 'notes', f"[{timestamp}] {new_note_text}")
                
                session.commit()
                print(f"✅ Note updated for {company.name}")
                return True
                
            except Exception as e:
                session.rollback()
                logger.error(f"Failed to update note: {e}")
                print(f"❌ Failed to update note: {e}")
                return False
    
    def delete_notes(self, company_name_or_ticker: str) -> bool:
        """Delete all notes for an IPO"""
        with get_db_session() as session:
            try:
                # Find the company and its IPO
                company = session.query(Company).filter(
                    (Company.name.ilike(f"%{company_name_or_ticker}%")) |
                    (Company.ticker_symbol.ilike(f"%{company_name_or_ticker}%"))
                ).first()
                
                if not company:
                    print(f"❌ Company '{company_name_or_ticker}' not found")
                    return False
                
                ipo = session.query(IPO).filter(
                    IPO.company_id == company.id
                ).order_by(IPO.created_at.desc()).first()
                
                if not ipo:
                    print(f"❌ No IPO found for company '{company.name}'")
                    return False
                
                # Delete the notes
                setattr(ipo, 'notes', None)
                
                session.commit()
                print(f"✅ Notes deleted for {company.name}")
                return True
                
            except Exception as e:
                session.rollback()
                logger.error(f"Failed to delete notes: {e}")
                print(f"❌ Failed to delete notes: {e}")
                return False

def main() -> None:
    """Interactive notes management"""
    notes_manager = NotesManager()
    
    while True:
        print("\n📝 IPO NOTES MANAGER")
        print("-" * 30)
        print("1. Add note to IPO")
        print("2. View all notes")
        print("3. View notes for specific company")
        print("4. Update note")
        print("5. Delete notes")
        print("6. Exit")
        
        choice = input("\nSelect an option (1-6): ").strip()
        
        if choice == "1":
            company = input("Enter company name or ticker: ").strip()
            note = input("Enter your note: ").strip()
            if company and note:
                notes_manager.add_note(company, note)
            else:
                print("❌ Please provide both company name and note")
        
        elif choice == "2":
            notes_manager.view_notes()
        
        elif choice == "3":
            company = input("Enter company name or ticker: ").strip()
            if company:
                notes_manager.view_notes(company)
            else:
                print("❌ Please provide company name or ticker")
        
        elif choice == "4":
            company = input("Enter company name or ticker: ").strip()
            note = input("Enter new note (this will replace all existing notes): ").strip()
            if company and note:
                notes_manager.update_note(company, note)
            else:
                print("❌ Please provide both company name and note")
        
        elif choice == "5":
            company = input("Enter company name or ticker: ").strip()
            if company:
                confirm = input(f"Are you sure you want to delete all notes for '{company}'? (y/N): ").strip().lower()
                if confirm == 'y':
                    notes_manager.delete_notes(company)
                else:
                    print("❌ Operation cancelled")
            else:
                print("❌ Please provide company name or ticker")
        
        elif choice == "6":
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid option. Please try again.")

if __name__ == "__main__":
    main()
